{"name": "bldaty-web", "version": "1.0.0", "description": "دليل بلدتي - منصة الويب", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "next-auth": "^4.24.5", "next-intl": "^3.4.0", "next-themes": "^0.2.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "react-rating-stars-component": "^2.2.0", "swiper": "^11.0.5", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "@googlemaps/js-api-loader": "^1.16.2", "date-fns": "^2.30.0", "react-intersection-observer": "^9.5.3", "react-share": "^5.0.3"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/leaflet": "^1.9.8", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0"}, "engines": {"node": ">=18.0.0"}}