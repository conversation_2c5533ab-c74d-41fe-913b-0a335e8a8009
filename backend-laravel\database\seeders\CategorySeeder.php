<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'slug' => 'civil-status',
                'name_ar' => 'الحالة المدنية',
                'name_fr' => 'État Civil',
                'name_en' => 'Civil Status',
                'description_ar' => 'مكاتب الحالة المدنية، استخراج الوثائق، شهادات الميلاد والوفاة',
                'description_fr' => 'Bureaux d\'état civil, extraction de documents, certificats de naissance et de décès',
                'icon' => 'identification',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'slug' => 'health',
                'name_ar' => 'المراكز الصحية',
                'name_fr' => 'Centres de Santé',
                'name_en' => 'Health Centers',
                'description_ar' => 'مستوصفات، صيدليات، عيادات، مراكز الأمومة والطفولة',
                'description_fr' => 'Dispensaires, pharmacies, cliniques, centres de maternité et d\'enfance',
                'icon' => 'heart',
                'color' => '#EF4444',
                'sort_order' => 2,
            ],
            [
                'slug' => 'education',
                'name_ar' => 'التعليم',
                'name_fr' => 'Éducation',
                'name_en' => 'Education',
                'description_ar' => 'مدارس ابتدائية، متوسطات، ثانويات، جامعات، مراكز التكوين',
                'description_fr' => 'Écoles primaires, collèges, lycées, universités, centres de formation',
                'icon' => 'academic-cap',
                'color' => '#10B981',
                'sort_order' => 3,
            ],
            [
                'slug' => 'mosques',
                'name_ar' => 'المساجد والزوايا',
                'name_fr' => 'Mosquées et Zaouïas',
                'name_en' => 'Mosques and Zaouias',
                'description_ar' => 'مساجد، زوايا، مدارس قرآنية، مراكز التعليم الديني',
                'description_fr' => 'Mosquées, zaouïas, écoles coraniques, centres d\'enseignement religieux',
                'icon' => 'building-library',
                'color' => '#059669',
                'sort_order' => 4,
            ],
            [
                'slug' => 'commerce',
                'name_ar' => 'التجارة والخدمات',
                'name_fr' => 'Commerce et Services',
                'name_en' => 'Commerce and Services',
                'description_ar' => 'محلات، مقاهي، مطاعم، خدمات الحلاقة والخياطة',
                'description_fr' => 'Magasins, cafés, restaurants, services de coiffure et couture',
                'icon' => 'shopping-bag',
                'color' => '#F59E0B',
                'sort_order' => 5,
            ],
            [
                'slug' => 'transport',
                'name_ar' => 'النقل',
                'name_fr' => 'Transport',
                'name_en' => 'Transportation',
                'description_ar' => 'مواقف، محطات الحافلات، تاكسي، كراء السيارات',
                'description_fr' => 'Parkings, stations de bus, taxis, location de voitures',
                'icon' => 'truck',
                'color' => '#8B5CF6',
                'sort_order' => 6,
            ],
            [
                'slug' => 'administration',
                'name_ar' => 'الإدارة',
                'name_fr' => 'Administration',
                'name_en' => 'Administration',
                'description_ar' => 'بلديات، مصالح الضرائب، المحاكم، الضمان الاجتماعي',
                'description_fr' => 'Mairies, services fiscaux, tribunaux, sécurité sociale',
                'icon' => 'building-office',
                'color' => '#6366F1',
                'sort_order' => 7,
            ],
            [
                'slug' => 'utilities',
                'name_ar' => 'المصالح التقنية',
                'name_fr' => 'Services Techniques',
                'name_en' => 'Utilities',
                'description_ar' => 'سونلغاز، الجزائرية للمياه، اتصالات الجزائر، البريد',
                'description_fr' => 'Sonelgaz, Algérienne des Eaux, Algérie Télécom, Poste',
                'icon' => 'bolt',
                'color' => '#FBBF24',
                'sort_order' => 8,
            ],
            [
                'slug' => 'craftsmen',
                'name_ar' => 'الحرفيون',
                'name_fr' => 'Artisans',
                'name_en' => 'Craftsmen',
                'description_ar' => 'سباكة، كهرباء، بناء، ديكور، نجارة، حدادة',
                'description_fr' => 'Plomberie, électricité, construction, décoration, menuiserie, ferronnerie',
                'icon' => 'wrench-screwdriver',
                'color' => '#6B7280',
                'sort_order' => 9,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
