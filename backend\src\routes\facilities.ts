import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { asyncHand<PERSON> } from '@/middleware/errorHandler'
import { FacilityController } from '@/controllers/FacilityController'

const router = Router()
const facilityController = new FacilityController()

/**
 * @swagger
 * components:
 *   schemas:
 *     Facility:
 *       type: object
 *       required:
 *         - nameAr
 *         - wilayaId
 *         - municipalityId
 *         - categoryId
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier
 *         nameAr:
 *           type: string
 *           description: Arabic name
 *         nameFr:
 *           type: string
 *           description: French name
 *         nameEn:
 *           type: string
 *           description: English name
 *         descriptionAr:
 *           type: string
 *           description: Arabic description
 *         address:
 *           type: string
 *           description: Physical address
 *         coordinates:
 *           type: array
 *           items:
 *             type: number
 *           description: [latitude, longitude]
 *         phone:
 *           type: string
 *           description: Phone number
 *         email:
 *           type: string
 *           description: Email address
 *         website:
 *           type: string
 *           description: Website URL
 *         isActive:
 *           type: boolean
 *           description: Whether facility is active
 *         averageRating:
 *           type: number
 *           description: Average rating (1-5)
 *         totalReviews:
 *           type: integer
 *           description: Total number of reviews
 */

/**
 * @swagger
 * /api/facilities:
 *   get:
 *     summary: Get all facilities with pagination and filters
 *     tags: [Facilities]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: wilayaId
 *         schema:
 *           type: string
 *         description: Filter by wilaya
 *       - in: query
 *         name: municipalityId
 *         schema:
 *           type: string
 *         description: Filter by municipality
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *     responses:
 *       200:
 *         description: List of facilities
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Facility'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 */
router.get(
  '/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('wilayaId').optional().isString().withMessage('Wilaya ID must be a string'),
    query('municipalityId').optional().isString().withMessage('Municipality ID must be a string'),
    query('categoryId').optional().isString().withMessage('Category ID must be a string'),
    query('search').optional().isString().withMessage('Search must be a string'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.getAllFacilities.bind(facilityController))
)

/**
 * @swagger
 * /api/facilities/{id}:
 *   get:
 *     summary: Get facility by ID
 *     tags: [Facilities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Facility ID
 *     responses:
 *       200:
 *         description: Facility details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Facility'
 *       404:
 *         description: Facility not found
 */
router.get(
  '/:id',
  [
    param('id').isString().notEmpty().withMessage('Facility ID is required'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.getFacilityById.bind(facilityController))
)

/**
 * @swagger
 * /api/facilities:
 *   post:
 *     summary: Create a new facility
 *     tags: [Facilities]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - nameAr
 *               - wilayaId
 *               - municipalityId
 *               - categoryId
 *             properties:
 *               nameAr:
 *                 type: string
 *               nameFr:
 *                 type: string
 *               nameEn:
 *                 type: string
 *               descriptionAr:
 *                 type: string
 *               address:
 *                 type: string
 *               coordinates:
 *                 type: array
 *                 items:
 *                   type: number
 *               phone:
 *                 type: string
 *               email:
 *                 type: string
 *               website:
 *                 type: string
 *               wilayaId:
 *                 type: string
 *               municipalityId:
 *                 type: string
 *               categoryId:
 *                 type: string
 *               subcategoryId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Facility created successfully
 *       400:
 *         description: Validation error
 */
router.post(
  '/',
  [
    body('nameAr').notEmpty().withMessage('Arabic name is required'),
    body('wilayaId').notEmpty().withMessage('Wilaya ID is required'),
    body('municipalityId').notEmpty().withMessage('Municipality ID is required'),
    body('categoryId').notEmpty().withMessage('Category ID is required'),
    body('email').optional().isEmail().withMessage('Invalid email format'),
    body('website').optional().isURL().withMessage('Invalid website URL'),
    body('coordinates').optional().isArray({ min: 2, max: 2 }).withMessage('Coordinates must be [latitude, longitude]'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.createFacility.bind(facilityController))
)

/**
 * @swagger
 * /api/facilities/{id}:
 *   put:
 *     summary: Update facility
 *     tags: [Facilities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Facility'
 *     responses:
 *       200:
 *         description: Facility updated successfully
 *       404:
 *         description: Facility not found
 */
router.put(
  '/:id',
  [
    param('id').isString().notEmpty().withMessage('Facility ID is required'),
    body('nameAr').optional().notEmpty().withMessage('Arabic name cannot be empty'),
    body('email').optional().isEmail().withMessage('Invalid email format'),
    body('website').optional().isURL().withMessage('Invalid website URL'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.updateFacility.bind(facilityController))
)

/**
 * @swagger
 * /api/facilities/{id}:
 *   delete:
 *     summary: Delete facility
 *     tags: [Facilities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Facility deleted successfully
 *       404:
 *         description: Facility not found
 */
router.delete(
  '/:id',
  [
    param('id').isString().notEmpty().withMessage('Facility ID is required'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.deleteFacility.bind(facilityController))
)

/**
 * @swagger
 * /api/facilities/{id}/view:
 *   post:
 *     summary: Increment facility view count
 *     tags: [Facilities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: View count incremented
 */
router.post(
  '/:id/view',
  [
    param('id').isString().notEmpty().withMessage('Facility ID is required'),
  ],
  validateRequest([]),
  asyncHandler(facilityController.incrementViewCount.bind(facilityController))
)

export default router
