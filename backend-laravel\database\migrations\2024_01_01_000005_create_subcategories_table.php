<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subcategories', function (Blueprint $table) {
            $table->id();
            $table->string('slug')->unique()->comment('URL-friendly identifier');
            $table->string('name_ar')->comment('Arabic name');
            $table->string('name_fr')->nullable()->comment('French name');
            $table->string('name_en')->nullable()->comment('English name');
            $table->text('description_ar')->nullable()->comment('Arabic description');
            $table->text('description_fr')->nullable()->comment('French description');
            $table->text('description_en')->nullable()->comment('English description');
            $table->string('icon')->nullable()->comment('Icon class or URL');
            $table->unsignedInteger('sort_order')->default(0)->comment('Display order');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['category_id', 'is_active', 'sort_order']);
            $table->index(['slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subcategories');
    }
};
