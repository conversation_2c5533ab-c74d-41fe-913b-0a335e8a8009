# 🏛️ دليل بلدتي - Backend Laravel

**الخادم الخلفي لمنصة دليل بلدتي باستخدام Laravel**

## 📖 نظرة عامة

هذا هو الخادم الخلفي لمنصة دليل بلدتي، مبني باستخدام Laravel 10 ويوفر API شامل لإدارة جميع المرافق والخدمات في بلديات الجزائر.

## ✨ المميزات الرئيسية

### 🏗️ البنية التقنية
- **Laravel 10** - إطار العمل الرئيسي
- **MySQL/PostgreSQL** - قاعدة البيانات
- **Redis** - التخزين المؤقت والجلسات
- **Laravel Sanctum** - المصادقة API
- **Spatie Permissions** - إدارة الأدوار والصلاحيات
- **Laravel Scout + Algolia** - البحث المتقدم
- **Spatie Media Library** - إدارة الملفات والصور

### 📊 إدارة البيانات
- **58 ولاية** جزائرية مع جميع البلديات
- **1,541 بلدية** مع التفاصيل الجغرافية
- **فئات وفئات فرعية** للمرافق والخدمات
- **نظام تقييمات** وتعليقات المستخدمين
- **نظام تقارير** لتحديث البيانات

### 🔐 الأمان والمصادقة
- JWT Authentication
- Role-based Access Control
- Rate Limiting
- Input Validation
- CORS Protection

## 🚀 التثبيت والإعداد

### المتطلبات
- PHP 8.1+
- Composer
- MySQL 8.0+ أو PostgreSQL 13+
- Redis
- Node.js (للأصول)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/bldaty-backend.git
cd bldaty-backend
```

2. **تثبيت التبعيات**
```bash
composer install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
php artisan key:generate
```

4. **تحرير ملف .env**
```env
APP_NAME="دليل بلدتي"
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bldaty_db
DB_USERNAME=root
DB_PASSWORD=

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

5. **إعداد قاعدة البيانات**
```bash
php artisan migrate
php artisan db:seed
```

6. **إعداد التخزين**
```bash
php artisan storage:link
```

7. **تشغيل الخادم**
```bash
php artisan serve
```

## 📚 API Documentation

### نقاط النهاية الرئيسية

#### المصادقة
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
GET  /api/v1/auth/me
```

#### المرافق
```
GET    /api/v1/facilities
POST   /api/v1/facilities
GET    /api/v1/facilities/{id}
PUT    /api/v1/facilities/{id}
DELETE /api/v1/facilities/{id}
```

#### البحث
```
GET /api/v1/search?query=مستوصف
GET /api/v1/search/suggestions
GET /api/v1/facilities/location/nearby?latitude=36.7538&longitude=3.0588
```

#### الولايات والبلديات
```
GET /api/v1/wilayas
GET /api/v1/wilayas/{id}/municipalities
GET /api/v1/municipalities
GET /api/v1/municipalities/{id}/facilities
```

#### الفئات
```
GET /api/v1/categories
GET /api/v1/categories/{id}/facilities
```

### مثال على الاستجابة
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name_ar": "مستوصف الحي",
    "name_fr": "Dispensaire du Quartier",
    "address": "شارع الاستقلال، الجزائر العاصمة",
    "coordinates": [36.7538, 3.0588],
    "phone": "+213 21 123 456",
    "average_rating": 4.2,
    "total_reviews": 15,
    "wilaya": {
      "id": 16,
      "name_ar": "الجزائر",
      "code": "16"
    },
    "category": {
      "id": 2,
      "name_ar": "المراكز الصحية",
      "slug": "health"
    }
  },
  "message": "Facility retrieved successfully"
}
```

## 🗄️ هيكل قاعدة البيانات

### الجداول الرئيسية

- **users** - المستخدمون
- **wilayas** - الولايات (58 ولاية)
- **municipalities** - البلديات (1,541 بلدية)
- **districts** - الأحياء
- **categories** - فئات المرافق
- **subcategories** - الفئات الفرعية
- **facilities** - المرافق والخدمات
- **reviews** - التقييمات والتعليقات
- **user_favorites** - المفضلة
- **reports** - التقارير

### العلاقات
```
Wilaya (1) -> (n) Municipality
Municipality (1) -> (n) District
Municipality (1) -> (n) Facility
Category (1) -> (n) Facility
User (1) -> (n) Review
Facility (1) -> (n) Review
```

## 🔧 الأوامر المفيدة

### إدارة قاعدة البيانات
```bash
# إنشاء migration جديد
php artisan make:migration create_table_name

# تشغيل migrations
php artisan migrate

# إعادة تعيين قاعدة البيانات
php artisan migrate:fresh --seed

# إنشاء seeder
php artisan make:seeder TableSeeder
```

### إدارة النماذج والمتحكمات
```bash
# إنشاء نموذج مع migration
php artisan make:model ModelName -m

# إنشاء متحكم API
php artisan make:controller Api/ControllerName --api

# إنشاء resource
php artisan make:resource ModelResource
```

### إدارة الصلاحيات
```bash
# إنشاء الأدوار والصلاحيات
php artisan permission:create-role admin
php artisan permission:create-permission "manage facilities"
```

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
php artisan test

# تشغيل اختبارات محددة
php artisan test --filter FacilityTest

# تشغيل الاختبارات مع التغطية
php artisan test --coverage
```

## 📈 المراقبة والأداء

### Laravel Telescope (التطوير)
```bash
php artisan telescope:install
php artisan migrate
```
الوصول: `http://localhost:8000/telescope`

### Laravel Horizon (الإنتاج)
```bash
php artisan horizon:install
php artisan horizon
```

### التخزين المؤقت
```bash
# مسح التخزين المؤقت
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🚀 النشر

### متطلبات الإنتاج
- PHP 8.1+ مع OPcache
- Nginx أو Apache
- MySQL/PostgreSQL
- Redis
- Supervisor (للطوابير)

### خطوات النشر
```bash
# تحديث التبعيات
composer install --optimize-autoloader --no-dev

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache

# تشغيل migrations
php artisan migrate --force

# تشغيل الطوابير
php artisan queue:work --daemon
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع**: https://bldaty.dz
- **البريد الإلكتروني**: <EMAIL>
- **GitHub**: https://github.com/bldaty

---

**صنع بـ ❤️ للجزائر وأهلها**
