import { Request, Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { CustomError } from '@/middleware/errorHandler'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export class FacilityController {
  async getAllFacilities(req: Request, res: Response) {
    const {
      page = 1,
      limit = 20,
      wilayaId,
      municipalityId,
      categoryId,
      subcategoryId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const skip = (pageNum - 1) * limitNum

    // Build where clause
    const where: any = {
      isActive: true
    }

    if (wilayaId) {
      where.wilayaId = wilayaId as string
    }

    if (municipalityId) {
      where.municipalityId = municipalityId as string
    }

    if (categoryId) {
      where.categoryId = categoryId as string
    }

    if (subcategoryId) {
      where.subcategoryId = subcategoryId as string
    }

    if (search) {
      where.OR = [
        { nameAr: { contains: search as string, mode: 'insensitive' } },
        { nameFr: { contains: search as string, mode: 'insensitive' } },
        { nameEn: { contains: search as string, mode: 'insensitive' } },
        { descriptionAr: { contains: search as string, mode: 'insensitive' } },
        { address: { contains: search as string, mode: 'insensitive' } }
      ]
    }

    // Build orderBy clause
    const orderBy: any = {}
    orderBy[sortBy as string] = sortOrder as string

    try {
      const [facilities, total] = await Promise.all([
        prisma.facility.findMany({
          where,
          skip,
          take: limitNum,
          orderBy,
          include: {
            wilaya: {
              select: { id: true, nameAr: true, nameFr: true, code: true }
            },
            municipality: {
              select: { id: true, nameAr: true, nameFr: true, code: true }
            },
            category: {
              select: { id: true, nameAr: true, nameFr: true, slug: true, icon: true }
            },
            subcategory: {
              select: { id: true, nameAr: true, nameFr: true, slug: true }
            },
            images: {
              where: { isMain: true },
              take: 1,
              select: { url: true, alt: true }
            },
            _count: {
              select: { reviews: true, favorites: true }
            }
          }
        }),
        prisma.facility.count({ where })
      ])

      const totalPages = Math.ceil(total / limitNum)

      logger.info(`Retrieved ${facilities.length} facilities`, {
        page: pageNum,
        limit: limitNum,
        total,
        filters: { wilayaId, municipalityId, categoryId, search }
      })

      res.json({
        success: true,
        data: facilities,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      })
    } catch (error) {
      logger.error('Error retrieving facilities', { error })
      throw new CustomError('Failed to retrieve facilities', 500)
    }
  }

  async getFacilityById(req: Request, res: Response) {
    const { id } = req.params

    try {
      const facility = await prisma.facility.findUnique({
        where: { id },
        include: {
          wilaya: {
            select: { id: true, nameAr: true, nameFr: true, code: true }
          },
          municipality: {
            select: { id: true, nameAr: true, nameFr: true, code: true }
          },
          district: {
            select: { id: true, nameAr: true, nameFr: true }
          },
          category: {
            select: { id: true, nameAr: true, nameFr: true, slug: true, icon: true }
          },
          subcategory: {
            select: { id: true, nameAr: true, nameFr: true, slug: true }
          },
          images: {
            orderBy: { sortOrder: 'asc' },
            select: { id: true, url: true, alt: true, caption: true, isMain: true }
          },
          workingHoursDetails: {
            orderBy: { dayOfWeek: 'asc' }
          },
          reviews: {
            where: { isApproved: true },
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              user: {
                select: { id: true, firstName: true, lastName: true, avatar: true }
              }
            }
          }
        }
      })

      if (!facility) {
        throw new CustomError('Facility not found', 404)
      }

      logger.info(`Retrieved facility details`, { facilityId: id })

      res.json({
        success: true,
        data: facility
      })
    } catch (error) {
      if (error instanceof CustomError) {
        throw error
      }
      logger.error('Error retrieving facility', { facilityId: id, error })
      throw new CustomError('Failed to retrieve facility', 500)
    }
  }

  async createFacility(req: Request, res: Response) {
    const facilityData = req.body

    try {
      // Validate that wilaya, municipality, and category exist
      const [wilaya, municipality, category] = await Promise.all([
        prisma.wilaya.findUnique({ where: { id: facilityData.wilayaId } }),
        prisma.municipality.findUnique({ where: { id: facilityData.municipalityId } }),
        prisma.category.findUnique({ where: { id: facilityData.categoryId } })
      ])

      if (!wilaya) {
        throw new CustomError('Wilaya not found', 404)
      }

      if (!municipality) {
        throw new CustomError('Municipality not found', 404)
      }

      if (!category) {
        throw new CustomError('Category not found', 404)
      }

      // Validate subcategory if provided
      if (facilityData.subcategoryId) {
        const subcategory = await prisma.subcategory.findUnique({
          where: { id: facilityData.subcategoryId }
        })
        if (!subcategory) {
          throw new CustomError('Subcategory not found', 404)
        }
      }

      const facility = await prisma.facility.create({
        data: facilityData,
        include: {
          wilaya: { select: { nameAr: true } },
          municipality: { select: { nameAr: true } },
          category: { select: { nameAr: true } }
        }
      })

      logger.info(`Created new facility`, { facilityId: facility.id, name: facility.nameAr })

      res.status(201).json({
        success: true,
        data: facility,
        message: 'Facility created successfully'
      })
    } catch (error) {
      if (error instanceof CustomError) {
        throw error
      }
      logger.error('Error creating facility', { facilityData, error })
      throw new CustomError('Failed to create facility', 500)
    }
  }

  async updateFacility(req: Request, res: Response) {
    const { id } = req.params
    const updateData = req.body

    try {
      // Check if facility exists
      const existingFacility = await prisma.facility.findUnique({
        where: { id }
      })

      if (!existingFacility) {
        throw new CustomError('Facility not found', 404)
      }

      const facility = await prisma.facility.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          wilaya: { select: { nameAr: true } },
          municipality: { select: { nameAr: true } },
          category: { select: { nameAr: true } }
        }
      })

      logger.info(`Updated facility`, { facilityId: id, name: facility.nameAr })

      res.json({
        success: true,
        data: facility,
        message: 'Facility updated successfully'
      })
    } catch (error) {
      if (error instanceof CustomError) {
        throw error
      }
      logger.error('Error updating facility', { facilityId: id, updateData, error })
      throw new CustomError('Failed to update facility', 500)
    }
  }

  async deleteFacility(req: Request, res: Response) {
    const { id } = req.params

    try {
      // Check if facility exists
      const existingFacility = await prisma.facility.findUnique({
        where: { id }
      })

      if (!existingFacility) {
        throw new CustomError('Facility not found', 404)
      }

      await prisma.facility.delete({
        where: { id }
      })

      logger.info(`Deleted facility`, { facilityId: id, name: existingFacility.nameAr })

      res.json({
        success: true,
        message: 'Facility deleted successfully'
      })
    } catch (error) {
      if (error instanceof CustomError) {
        throw error
      }
      logger.error('Error deleting facility', { facilityId: id, error })
      throw new CustomError('Failed to delete facility', 500)
    }
  }

  async incrementViewCount(req: Request, res: Response) {
    const { id } = req.params

    try {
      await prisma.facility.update({
        where: { id },
        data: {
          viewCount: {
            increment: 1
          }
        }
      })

      res.json({
        success: true,
        message: 'View count incremented'
      })
    } catch (error) {
      logger.error('Error incrementing view count', { facilityId: id, error })
      throw new CustomError('Failed to increment view count', 500)
    }
  }
}
