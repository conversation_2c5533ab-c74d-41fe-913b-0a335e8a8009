<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('facilities', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar')->comment('Arabic name');
            $table->string('name_fr')->nullable()->comment('French name');
            $table->string('name_en')->nullable()->comment('English name');
            $table->text('description_ar')->nullable()->comment('Arabic description');
            $table->text('description_fr')->nullable()->comment('French description');
            $table->text('description_en')->nullable()->comment('English description');
            $table->text('address')->nullable()->comment('Physical address');
            $table->json('coordinates')->nullable()->comment('Latitude and longitude');
            $table->string('phone')->nullable()->comment('Phone number');
            $table->string('email')->nullable()->comment('Email address');
            $table->string('website')->nullable()->comment('Website URL');
            $table->string('facebook_page')->nullable()->comment('Facebook page URL');
            $table->json('working_hours')->nullable()->comment('Working hours structure');
            $table->boolean('is_open_24h')->default(false)->comment('Open 24 hours');
            $table->boolean('is_active')->default(true)->comment('Is facility active');
            $table->boolean('is_verified')->default(false)->comment('Is facility verified');
            $table->timestamp('verified_at')->nullable()->comment('Verification timestamp');
            $table->unsignedBigInteger('view_count')->default(0)->comment('Number of views');
            $table->decimal('average_rating', 3, 2)->default(0)->comment('Average rating (0-5)');
            $table->unsignedInteger('total_reviews')->default(0)->comment('Total number of reviews');
            
            // Foreign keys
            $table->foreignId('wilaya_id')->constrained()->onDelete('cascade');
            $table->foreignId('municipality_id')->constrained()->onDelete('cascade');
            $table->foreignId('district_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('subcategory_id')->nullable()->constrained()->onDelete('set null');
            
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_active', 'is_verified']);
            $table->index(['wilaya_id', 'municipality_id', 'is_active']);
            $table->index(['category_id', 'is_active']);
            $table->index(['average_rating', 'total_reviews']);
            $table->index(['view_count']);
            $table->index(['created_at']);
            
            // Full-text search indexes
            $table->fullText(['name_ar', 'name_fr', 'name_en', 'description_ar', 'address']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('facilities');
    }
};
