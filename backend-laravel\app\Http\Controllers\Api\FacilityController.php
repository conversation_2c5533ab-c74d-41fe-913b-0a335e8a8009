<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreFacilityRequest;
use App\Http\Requests\UpdateFacilityRequest;
use App\Http\Resources\FacilityResource;
use App\Http\Resources\FacilityCollection;
use App\Models\Facility;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class FacilityController extends Controller
{
    /**
     * Display a listing of facilities.
     */
    public function index(Request $request): JsonResponse
    {
        $facilities = QueryBuilder::for(Facility::class)
            ->allowedFilters([
                'name_ar',
                'name_fr',
                'name_en',
                'address',
                AllowedFilter::exact('wilaya_id'),
                AllowedFilter::exact('municipality_id'),
                AllowedFilter::exact('district_id'),
                AllowedFilter::exact('category_id'),
                AllowedFilter::exact('subcategory_id'),
                AllowedFilter::exact('is_verified'),
                AllowedFilter::scope('active'),
                AllowedFilter::scope('verified'),
            ])
            ->allowedSorts([
                'name_ar',
                'created_at',
                'updated_at',
                'average_rating',
                'view_count',
                'total_reviews'
            ])
            ->allowedIncludes([
                'wilaya',
                'municipality',
                'district',
                'category',
                'subcategory',
                'media'
            ])
            ->with([
                'wilaya:id,name_ar,name_fr,code',
                'municipality:id,name_ar,name_fr,code',
                'category:id,name_ar,name_fr,slug,icon',
                'subcategory:id,name_ar,name_fr,slug'
            ])
            ->active()
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => new FacilityCollection($facilities),
            'message' => 'Facilities retrieved successfully'
        ]);
    }

    /**
     * Store a newly created facility.
     */
    public function store(StoreFacilityRequest $request): JsonResponse
    {
        $facility = Facility::create($request->validated());

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $facility->addMediaFromRequest("images.{$index}")
                         ->toMediaCollection('images');
            }
        }

        $facility->load([
            'wilaya:id,name_ar,name_fr',
            'municipality:id,name_ar,name_fr',
            'category:id,name_ar,name_fr,slug',
            'subcategory:id,name_ar,name_fr,slug'
        ]);

        return response()->json([
            'success' => true,
            'data' => new FacilityResource($facility),
            'message' => 'Facility created successfully'
        ], 201);
    }

    /**
     * Display the specified facility.
     */
    public function show(Facility $facility): JsonResponse
    {
        // Increment view count
        $facility->incrementViewCount();

        $facility->load([
            'wilaya:id,name_ar,name_fr,code',
            'municipality:id,name_ar,name_fr,code',
            'district:id,name_ar,name_fr',
            'category:id,name_ar,name_fr,slug,icon',
            'subcategory:id,name_ar,name_fr,slug',
            'workingHoursDetails',
            'approvedReviews' => function ($query) {
                $query->latest()->take(5)->with('user:id,first_name,last_name,avatar');
            }
        ]);

        return response()->json([
            'success' => true,
            'data' => new FacilityResource($facility),
            'message' => 'Facility retrieved successfully'
        ]);
    }

    /**
     * Update the specified facility.
     */
    public function update(UpdateFacilityRequest $request, Facility $facility): JsonResponse
    {
        $facility->update($request->validated());

        // Handle image uploads
        if ($request->hasFile('images')) {
            // Clear existing images if specified
            if ($request->boolean('replace_images')) {
                $facility->clearMediaCollection('images');
            }

            foreach ($request->file('images') as $index => $image) {
                $facility->addMediaFromRequest("images.{$index}")
                         ->toMediaCollection('images');
            }
        }

        $facility->load([
            'wilaya:id,name_ar,name_fr',
            'municipality:id,name_ar,name_fr',
            'category:id,name_ar,name_fr,slug',
            'subcategory:id,name_ar,name_fr,slug'
        ]);

        return response()->json([
            'success' => true,
            'data' => new FacilityResource($facility),
            'message' => 'Facility updated successfully'
        ]);
    }

    /**
     * Remove the specified facility.
     */
    public function destroy(Facility $facility): JsonResponse
    {
        $facility->delete();

        return response()->json([
            'success' => true,
            'message' => 'Facility deleted successfully'
        ]);
    }

    /**
     * Get facilities by location.
     */
    public function byLocation(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:50', // km
            'category_id' => 'nullable|exists:categories,id',
        ]);

        $latitude = $request->latitude;
        $longitude = $request->longitude;
        $radius = $request->get('radius', 5); // Default 5km

        $facilities = Facility::selectRaw("
                *,
                (6371 * acos(cos(radians(?)) * cos(radians(JSON_EXTRACT(coordinates, '$[0]'))) 
                * cos(radians(JSON_EXTRACT(coordinates, '$[1]')) - radians(?)) 
                + sin(radians(?)) * sin(radians(JSON_EXTRACT(coordinates, '$[0]'))))) AS distance
            ", [$latitude, $longitude, $latitude])
            ->whereNotNull('coordinates')
            ->havingRaw('distance <= ?', [$radius])
            ->when($request->category_id, function ($query) use ($request) {
                return $query->where('category_id', $request->category_id);
            })
            ->active()
            ->with([
                'wilaya:id,name_ar,name_fr',
                'municipality:id,name_ar,name_fr',
                'category:id,name_ar,name_fr,slug,icon'
            ])
            ->orderBy('distance')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => new FacilityCollection($facilities),
            'message' => 'Nearby facilities retrieved successfully'
        ]);
    }

    /**
     * Search facilities.
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2',
            'wilaya_id' => 'nullable|exists:wilayas,id',
            'municipality_id' => 'nullable|exists:municipalities,id',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        $facilities = Facility::search($request->query)
            ->when($request->wilaya_id, function ($query) use ($request) {
                return $query->where('wilaya_id', $request->wilaya_id);
            })
            ->when($request->municipality_id, function ($query) use ($request) {
                return $query->where('municipality_id', $request->municipality_id);
            })
            ->when($request->category_id, function ($query) use ($request) {
                return $query->where('category_id', $request->category_id);
            })
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => new FacilityCollection($facilities),
            'message' => 'Search results retrieved successfully'
        ]);
    }

    /**
     * Get popular facilities.
     */
    public function popular(Request $request): JsonResponse
    {
        $facilities = Facility::active()
            ->verified()
            ->orderByDesc('view_count')
            ->orderByDesc('average_rating')
            ->with([
                'wilaya:id,name_ar,name_fr',
                'municipality:id,name_ar,name_fr',
                'category:id,name_ar,name_fr,slug,icon'
            ])
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => new FacilityCollection($facilities),
            'message' => 'Popular facilities retrieved successfully'
        ]);
    }

    /**
     * Get recently added facilities.
     */
    public function recent(Request $request): JsonResponse
    {
        $facilities = Facility::active()
            ->latest()
            ->with([
                'wilaya:id,name_ar,name_fr',
                'municipality:id,name_ar,name_fr',
                'category:id,name_ar,name_fr,slug,icon'
            ])
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => new FacilityCollection($facilities),
            'message' => 'Recent facilities retrieved successfully'
        ]);
    }
}
