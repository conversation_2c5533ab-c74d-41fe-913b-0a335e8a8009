import { Request, Response, NextFunction } from 'express'
import { validation<PERSON><PERSON><PERSON>, Validation<PERSON>hain } from 'express-validator'
import { CustomError } from './errorHandler'

export const validateRequest = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)))

    const errors = validationResult(req)
    if (errors.isEmpty()) {
      return next()
    }

    // Format errors
    const formattedErrors = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }))

    const error = new CustomError('Validation failed', 400)
    ;(error as any).details = formattedErrors

    next(error)
  }
}
