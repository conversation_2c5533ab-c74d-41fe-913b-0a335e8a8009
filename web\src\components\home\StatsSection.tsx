'use client'

import { useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'

const stats = [
  {
    id: 'wilayas',
    number: 58,
    label: 'ولاية',
    description: 'تغطية شاملة لجميع ولايات الجزائر',
    icon: '🏛️'
  },
  {
    id: 'municipalities',
    number: 1541,
    label: 'بلدية',
    description: 'جميع البلديات الجزائرية مغطاة',
    icon: '🏘️'
  },
  {
    id: 'facilities',
    number: 75000,
    label: 'مرفق وخدمة',
    description: 'قاعدة بيانات شاملة ومحدثة',
    icon: '🏢'
  },
  {
    id: 'users',
    number: 250000,
    label: 'مستخدم نشط',
    description: 'مجتمع متنامي من المواطنين',
    icon: '👥'
  },
  {
    id: 'reviews',
    number: 45000,
    label: 'تقييم وتعليق',
    description: 'تجارب حقيقية من المواطنين',
    icon: '⭐'
  },
  {
    id: 'updates',
    number: 1200,
    label: 'تحديث يومي',
    description: 'معلومات محدثة باستمرار',
    icon: '🔄'
  }
]

function AnimatedNumber({ 
  target, 
  duration = 2000, 
  isVisible 
}: { 
  target: number
  duration?: number
  isVisible: boolean 
}) {
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    const increment = target / (duration / 16)
    let current = 0
    
    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        setCurrent(target)
        clearInterval(timer)
      } else {
        setCurrent(Math.floor(current))
      }
    }, 16)

    return () => clearInterval(timer)
  }, [target, duration, isVisible])

  return (
    <span>
      {current.toLocaleString('ar-DZ')}
      {target >= 1000 && current === target && '+'}
    </span>
  )
}

export function StatsSection() {
  const { ref, inView } = useInView({
    threshold: 0.3,
    triggerOnce: true
  })

  return (
    <section ref={ref} className="py-20 bg-gradient-to-br from-gray-900 via-primary-900 to-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/patterns/stats-pattern.svg')] opacity-5"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-arabic">
            أرقام تتحدث عن نفسها
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto font-arabic leading-relaxed">
            منصة دليل بلدتي تنمو يوماً بعد يوم لتصبح المرجع الأول للمواطنين الجزائريين
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div
              key={stat.id}
              className="text-center group"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                {/* Icon */}
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                
                {/* Number */}
                <div className="text-4xl md:text-5xl font-bold text-secondary-400 mb-2 font-mono">
                  <AnimatedNumber target={stat.number} isVisible={inView} />
                </div>
                
                {/* Label */}
                <div className="text-xl font-semibold text-white mb-3 font-arabic">
                  {stat.label}
                </div>
                
                {/* Description */}
                <p className="text-gray-300 font-arabic leading-relaxed">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Achievement */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 font-arabic">
              🏆 المنصة الرائدة في الجزائر
            </h3>
            <p className="text-lg text-secondary-100 font-arabic leading-relaxed">
              أول منصة رقمية شاملة تجمع جميع المرافق والخدمات في كل بلديات الجزائر في مكان واحد
            </p>
            <div className="flex flex-wrap justify-center gap-4 mt-6">
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-4 py-2">
                <span className="text-2xl">🥇</span>
                <span className="font-arabic">الأولى في التغطية</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-4 py-2">
                <span className="text-2xl">🚀</span>
                <span className="font-arabic">الأسرع في التحديث</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-4 py-2">
                <span className="text-2xl">💯</span>
                <span className="font-arabic">الأدق في المعلومات</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
