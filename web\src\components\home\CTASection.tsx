'use client'

import { useState } from 'react'
import { 
  DevicePhoneMobileIcon, 
  ComputerDesktopIcon,
  ArrowDownTrayIcon,
  QrCodeIcon
} from '@heroicons/react/24/outline'

export function CTASection() {
  const [showQR, setShowQR] = useState(false)

  return (
    <section className="py-20 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/patterns/cta-pattern.svg')] opacity-10"></div>
      
      {/* Algeria Flag Accent */}
      <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-algeria-green via-algeria-white to-algeria-red"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Main CTA */}
          <div className="mb-16">
            <h2 className="text-4xl md:text-5xl font-bold font-arabic mb-6 leading-tight">
              ابدأ رحلتك مع دليل بلدتي اليوم
            </h2>
            <p className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto font-arabic leading-relaxed">
              انضم إلى مئات الآلاف من المواطنين الجزائريين واكتشف جميع المرافق والخدمات في بلديتك
            </p>
            
            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <button className="flex items-center gap-3 bg-white text-primary-700 hover:bg-gray-100 px-8 py-4 rounded-xl font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                <DevicePhoneMobileIcon className="w-6 h-6" />
                <div className="text-right">
                  <div className="text-sm text-gray-600">حمل التطبيق من</div>
                  <div className="font-bold font-arabic">Google Play</div>
                </div>
              </button>
              
              <button className="flex items-center gap-3 bg-white text-primary-700 hover:bg-gray-100 px-8 py-4 rounded-xl font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                <DevicePhoneMobileIcon className="w-6 h-6" />
                <div className="text-right">
                  <div className="text-sm text-gray-600">حمل التطبيق من</div>
                  <div className="font-bold font-arabic">App Store</div>
                </div>
              </button>
              
              <button 
                onClick={() => setShowQR(!showQR)}
                className="flex items-center gap-3 bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-4 rounded-xl font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl"
              >
                <QrCodeIcon className="w-6 h-6" />
                <span className="font-arabic">رمز QR</span>
              </button>
            </div>

            {/* QR Code */}
            {showQR && (
              <div className="inline-block bg-white p-6 rounded-2xl shadow-xl animate-fade-in">
                <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center mb-4">
                  <QrCodeIcon className="w-32 h-32 text-gray-400" />
                </div>
                <p className="text-gray-700 font-arabic text-sm">
                  امسح الرمز لتحميل التطبيق
                </p>
              </div>
            )}
          </div>

          {/* Web Platform CTA */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 max-w-4xl mx-auto mb-16">
            <div className="flex items-center justify-center gap-4 mb-6">
              <ComputerDesktopIcon className="w-12 h-12 text-secondary-400" />
              <h3 className="text-2xl font-bold font-arabic">
                أو استخدم المنصة الإلكترونية
              </h3>
            </div>
            <p className="text-primary-100 mb-6 font-arabic leading-relaxed">
              لا تحتاج لتحميل أي شيء - استخدم المنصة مباشرة من متصفحك على الكمبيوتر أو الهاتف
            </p>
            <button className="btn-secondary px-8 py-3 text-lg font-arabic">
              ابدأ الاستكشاف الآن
            </button>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🆓</span>
              </div>
              <h4 className="text-xl font-semibold mb-2 font-arabic">مجاني تماماً</h4>
              <p className="text-primary-100 font-arabic">
                جميع الخدمات متاحة مجاناً بدون أي رسوم أو اشتراكات
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h4 className="text-xl font-semibold mb-2 font-arabic">سريع وسهل</h4>
              <p className="text-primary-100 font-arabic">
                واجهة بسيطة وسريعة تمكنك من العثور على ما تريد في ثوانٍ
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔄</span>
              </div>
              <h4 className="text-xl font-semibold mb-2 font-arabic">محدث باستمرار</h4>
              <p className="text-primary-100 font-arabic">
                معلومات محدثة يومياً لضمان دقة البيانات
              </p>
            </div>
          </div>

          {/* Social Proof */}
          <div className="bg-white/5 rounded-2xl p-8 border border-white/10">
            <h4 className="text-xl font-semibold mb-6 font-arabic">
              انضم إلى مجتمعنا المتنامي
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-400 mb-1">250K+</div>
                <div className="text-primary-100 font-arabic text-sm">مستخدم نشط</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-400 mb-1">45K+</div>
                <div className="text-primary-100 font-arabic text-sm">تقييم إيجابي</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-400 mb-1">4.8</div>
                <div className="text-primary-100 font-arabic text-sm">تقييم التطبيق</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-400 mb-1">1M+</div>
                <div className="text-primary-100 font-arabic text-sm">عملية بحث شهرياً</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
