<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Category extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'slug',
        'name_ar',
        'name_fr',
        'name_en',
        'description_ar',
        'description_fr',
        'description_en',
        'icon',
        'color',
        'sort_order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the subcategories for the category.
     */
    public function subcategories()
    {
        return $this->hasMany(Subcategory::class);
    }

    /**
     * Get the facilities for the category.
     */
    public function facilities()
    {
        return $this->hasMany(Facility::class);
    }

    /**
     * Get the active subcategories for the category.
     */
    public function activeSubcategories()
    {
        return $this->subcategories()->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get the active facilities for the category.
     */
    public function activeFacilities()
    {
        return $this->facilities()->where('is_active', true);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name_ar');
    }

    /**
     * Get the category's name in the specified language.
     */
    public function getName($locale = 'ar'): string
    {
        return match($locale) {
            'fr' => $this->name_fr ?? $this->name_ar,
            'en' => $this->name_en ?? $this->name_ar,
            default => $this->name_ar,
        };
    }

    /**
     * Get the category's description in the specified language.
     */
    public function getDescription($locale = 'ar'): ?string
    {
        return match($locale) {
            'fr' => $this->description_fr ?? $this->description_ar,
            'en' => $this->description_en ?? $this->description_ar,
            default => $this->description_ar,
        };
    }

    /**
     * Get the total number of facilities in the category.
     */
    public function getFacilitiesCountAttribute(): int
    {
        return $this->facilities()->where('is_active', true)->count();
    }

    /**
     * Get the total number of subcategories in the category.
     */
    public function getSubcategoriesCountAttribute(): int
    {
        return $this->subcategories()->where('is_active', true)->count();
    }

    /**
     * Get the category icon URL.
     */
    public function getIconUrlAttribute(): ?string
    {
        if ($this->hasMedia('icons')) {
            return $this->getFirstMediaUrl('icons');
        }
        
        return $this->icon;
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('icons')
              ->singleFile()
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml']);
    }
}
