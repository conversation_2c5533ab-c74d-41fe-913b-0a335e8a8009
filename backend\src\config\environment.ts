import dotenv from 'dotenv'
import path from 'path'

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') })

interface Config {
  nodeEnv: string
  port: number
  database: {
    url: string
    host: string
    port: number
    name: string
    user: string
    password: string
  }
  jwt: {
    secret: string
    expiresIn: string
  }
  cors: {
    origin: string
  }
  rateLimit: {
    windowMs: number
    maxRequests: number
  }
  upload: {
    maxFileSize: number
    allowedTypes: string[]
  }
  cloudinary: {
    cloudName: string
    apiKey: string
    apiSecret: string
  }
  email: {
    host: string
    port: number
    user: string
    pass: string
  }
  redis: {
    url: string
  }
  googleMaps: {
    apiKey: string
  }
}

const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'CLOUDINARY_CLOUD_NAME',
  'CLOUDINARY_API_KEY',
  'CLOUDINARY_API_SECRET'
]

// Check for required environment variables
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`)
  }
}

export const config: Config = {
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3001', 10),
  
  database: {
    url: process.env.DATABASE_URL!,
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432', 10),
    name: process.env.DATABASE_NAME || 'bldaty_db',
    user: process.env.DATABASE_USER || 'bldaty_user',
    password: process.env.DATABASE_PASSWORD || 'bldaty_password'
  },
  
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000,http://localhost:3002'
  },
  
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
  },
  
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880', 10), // 5MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(',')
  },
  
  cloudinary: {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME!,
    apiKey: process.env.CLOUDINARY_API_KEY!,
    apiSecret: process.env.CLOUDINARY_API_SECRET!
  },
  
  email: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || ''
  },
  
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379'
  },
  
  googleMaps: {
    apiKey: process.env.GOOGLE_MAPS_API_KEY || ''
  }
}

// Validate configuration
if (config.port < 1 || config.port > 65535) {
  throw new Error('PORT must be a valid port number between 1 and 65535')
}

if (config.database.port < 1 || config.database.port > 65535) {
  throw new Error('DATABASE_PORT must be a valid port number between 1 and 65535')
}

if (config.upload.maxFileSize < 1) {
  throw new Error('MAX_FILE_SIZE must be a positive number')
}

if (config.rateLimit.windowMs < 1000) {
  throw new Error('RATE_LIMIT_WINDOW_MS must be at least 1000 milliseconds')
}

if (config.rateLimit.maxRequests < 1) {
  throw new Error('RATE_LIMIT_MAX_REQUESTS must be at least 1')
}
