'use client'

import { useState, useRef, useEffect } from 'react'
import { MagnifyingGlassIcon, XMarkIcon, ClockIcon } from '@heroicons/react/24/outline'
import { useDebounce } from '@/hooks/useDebounce'

interface SearchBarProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  autoFocus?: boolean
  onSearch?: (query: string) => void
}

interface SearchSuggestion {
  id: string
  text: string
  type: 'facility' | 'category' | 'location' | 'recent'
  category?: string
  location?: string
}

const mockSuggestions: SearchSuggestion[] = [
  { id: '1', text: 'مستوصف الحي', type: 'facility', category: 'صحة', location: 'الجزائر العاصمة' },
  { id: '2', text: 'مدرسة ابتدائية', type: 'category', category: 'تعليم' },
  { id: '3', text: 'صيدلية', type: 'category', category: 'صحة' },
  { id: '4', text: 'مسجد النور', type: 'facility', category: 'مساجد', location: 'وهران' },
  { id: '5', text: 'الحالة المدنية', type: 'category', category: 'إدارة' },
  { id: '6', text: 'بلدية باب الوادي', type: 'location', location: 'الجزائر العاصمة' },
]

const recentSearches = [
  'مستوصف قريب',
  'صيدلية مفتوحة',
  'مدرسة ابتدائية',
  'مسجد الحي'
]

export function SearchBar({
  value = '',
  onChange,
  placeholder = 'ابحث عن مرفق أو خدمة...',
  className = '',
  size = 'md',
  autoFocus = false,
  onSearch
}: SearchBarProps) {
  const [query, setQuery] = useState(value)
  const [isOpen, setIsOpen] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const debouncedQuery = useDebounce(query, 300)

  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  }

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  useEffect(() => {
    if (debouncedQuery.length > 0) {
      // Simulate API call for suggestions
      const filtered = mockSuggestions.filter(suggestion =>
        suggestion.text.includes(debouncedQuery) ||
        suggestion.category?.includes(debouncedQuery)
      )
      setSuggestions(filtered)
    } else {
      setSuggestions([])
    }
  }, [debouncedQuery])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setQuery(newValue)
    onChange?.(newValue)
    setIsOpen(true)
    setSelectedIndex(-1)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          handleSuggestionClick(suggestions[selectedIndex])
        } else {
          handleSearch()
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    onChange?.(suggestion.text)
    setIsOpen(false)
    setSelectedIndex(-1)
    onSearch?.(suggestion.text)
  }

  const handleRecentClick = (recent: string) => {
    setQuery(recent)
    onChange?.(recent)
    setIsOpen(false)
    onSearch?.(recent)
  }

  const handleSearch = () => {
    if (query.trim()) {
      onSearch?.(query.trim())
      setIsOpen(false)
    }
  }

  const clearSearch = () => {
    setQuery('')
    onChange?.('')
    setIsOpen(false)
    inputRef.current?.focus()
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'facility':
        return '🏢'
      case 'category':
        return '📂'
      case 'location':
        return '📍'
      case 'recent':
        return '🕒'
      default:
        return '🔍'
    }
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className={`
            block w-full pr-10 pl-4 border border-gray-300 dark:border-gray-600 
            rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent 
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 
            placeholder-gray-500 dark:placeholder-gray-400 font-arabic
            ${sizeClasses[size]}
          `}
          dir="rtl"
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 left-0 pl-3 flex items-center"
          >
            <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Recent Searches */}
          {query.length === 0 && recentSearches.length > 0 && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-2 font-arabic">
                <ClockIcon className="w-4 h-4" />
                عمليات البحث الأخيرة
              </div>
              {recentSearches.map((recent, index) => (
                <button
                  key={index}
                  onClick={() => handleRecentClick(recent)}
                  className="block w-full text-right px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded font-arabic"
                >
                  {recent}
                </button>
              ))}
            </div>
          )}

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <div className="p-2">
              {suggestions.map((suggestion, index) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={`
                    w-full flex items-center gap-3 px-3 py-3 text-right rounded-lg transition-colors duration-150 font-arabic
                    ${index === selectedIndex 
                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' 
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }
                  `}
                >
                  <span className="text-lg">{getSuggestionIcon(suggestion.type)}</span>
                  <div className="flex-1 text-right">
                    <div className="font-medium">{suggestion.text}</div>
                    {suggestion.category && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {suggestion.category}
                        {suggestion.location && ` • ${suggestion.location}`}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* No Results */}
          {query.length > 0 && suggestions.length === 0 && (
            <div className="p-6 text-center text-gray-500 dark:text-gray-400 font-arabic">
              <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>لم يتم العثور على نتائج لـ "{query}"</p>
              <p className="text-sm mt-1">جرب كلمات مختلفة أو تحقق من الإملاء</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
