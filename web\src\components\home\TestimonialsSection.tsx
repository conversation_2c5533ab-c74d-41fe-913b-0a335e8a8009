'use client'

import { useState } from 'react'
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid'
import { QuoteIcon } from '@heroicons/react/24/outline'

const testimonials = [
  {
    id: 1,
    name: 'أحمد بن علي',
    location: 'الجزائر العاصمة',
    avatar: '/avatars/ahmed.jpg',
    rating: 5,
    text: 'منصة رائعة! وفرت علي الكثير من الوقت في البحث عن المرافق في حيي. الآن أعرف مواعيد عمل جميع المصالح وأقرب صيدلية مفتوحة.',
    category: 'مواطن'
  },
  {
    id: 2,
    name: 'فاطمة الزهراء',
    location: 'وهران',
    avatar: '/avatars/fatima.jpg',
    rating: 5,
    text: 'كأم لثلاثة أطفال، هذا التطبيق ساعدني كثيراً في العثور على أقرب مدرسة ومركز صحي. المعلومات دقيقة ومحدثة باستمرار.',
    category: 'أم عاملة'
  },
  {
    id: 3,
    name: 'محمد الطاهر',
    location: 'قسنطينة',
    avatar: '/avatars/mohamed.jpg',
    rating: 5,
    text: 'بصفتي طالب جامعي، أستخدم التطبيق للعثور على المكتبات والمقاهي القريبة من الجامعة. الخرائط التفاعلية مفيدة جداً.',
    category: 'طالب جامعي'
  },
  {
    id: 4,
    name: 'خديجة بوعلام',
    location: 'عنابة',
    avatar: '/avatars/khadija.jpg',
    rating: 5,
    text: 'العمل في التجارة يتطلب معرفة جميع المرافق في المنطقة. دليل بلدتي يوفر لي كل ما أحتاجه من معلومات عن الموردين والخدمات.',
    category: 'تاجرة'
  },
  {
    id: 5,
    name: 'يوسف مرادي',
    location: 'سطيف',
    avatar: '/avatars/youssef.jpg',
    rating: 5,
    text: 'كمسافر كثيراً بين الولايات، هذا التطبيق دليلي في كل مدينة أزورها. أجد بسرعة الفنادق والمطاعم والخدمات التي أحتاجها.',
    category: 'مسافر'
  },
  {
    id: 6,
    name: 'زينب حمدي',
    location: 'تلمسان',
    avatar: '/avatars/zineb.jpg',
    rating: 5,
    text: 'التطبيق سهل الاستخدام حتى لكبار السن مثلي. أستطيع العثور على أقرب مسجد أو صيدلية بضغطة زر واحدة.',
    category: 'متقاعدة'
  }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  // Auto-play functionality
  useState(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(nextTestimonial, 5000)
    return () => clearInterval(interval)
  })

  const currentTestimonial = testimonials[currentIndex]

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-arabic mb-4">
            ماذا يقول مستخدمونا
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto font-arabic leading-relaxed">
            تجارب حقيقية من مواطنين جزائريين يستخدمون منصة دليل بلدتي يومياً
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 md:p-12 shadow-soft border border-gray-200 dark:border-gray-700 relative">
            {/* Quote Icon */}
            <div className="absolute top-6 right-6 text-primary-200 dark:text-primary-800">
              <QuoteIcon className="w-12 h-12" />
            </div>

            {/* Rating */}
            <div className="flex items-center gap-1 mb-6">
              {[...Array(currentTestimonial.rating)].map((_, i) => (
                <StarIcon key={i} className="w-5 h-5 text-yellow-400" />
              ))}
            </div>

            {/* Testimonial Text */}
            <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 font-arabic leading-relaxed mb-8">
              "{currentTestimonial.text}"
            </blockquote>

            {/* Author Info */}
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                {currentTestimonial.name.charAt(0)}
              </div>
              <div>
                <div className="font-semibold text-gray-900 dark:text-white font-arabic text-lg">
                  {currentTestimonial.name}
                </div>
                <div className="text-gray-600 dark:text-gray-400 font-arabic">
                  {currentTestimonial.location} • {currentTestimonial.category}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <button
            onClick={prevTestimonial}
            className="p-2 rounded-full bg-white dark:bg-gray-800 shadow-soft border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            aria-label="التقييم السابق"
          >
            <ChevronRightIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>

          {/* Dots Indicator */}
          <div className="flex gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === currentIndex
                    ? 'bg-primary-600'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
                aria-label={`الانتقال للتقييم ${index + 1}`}
              />
            ))}
          </div>

          <button
            onClick={nextTestimonial}
            className="p-2 rounded-full bg-white dark:bg-gray-800 shadow-soft border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            aria-label="التقييم التالي"
          >
            <ChevronLeftIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.slice(0, 6).map((testimonial, index) => (
            <div
              key={testimonial.id}
              className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-soft border border-gray-200 dark:border-gray-700 cursor-pointer transition-all duration-300 hover:shadow-medium hover:-translate-y-1 ${
                index === currentIndex ? 'ring-2 ring-primary-500' : ''
              }`}
              onClick={() => goToTestimonial(index)}
            >
              {/* Rating */}
              <div className="flex items-center gap-1 mb-3">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <StarIcon key={i} className="w-4 h-4 text-yellow-400" />
                ))}
              </div>

              {/* Text */}
              <p className="text-gray-700 dark:text-gray-300 font-arabic leading-relaxed mb-4 line-clamp-3">
                "{testimonial.text}"
              </p>

              {/* Author */}
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {testimonial.name.charAt(0)}
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white font-arabic text-sm">
                    {testimonial.name}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400 font-arabic text-xs">
                    {testimonial.location}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
