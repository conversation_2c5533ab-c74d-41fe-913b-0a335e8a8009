{"name": "b<PERSON><PERSON>", "version": "1.0.0", "description": "دليل بلدتي - منصة شاملة لجميع بلديات الجزائر", "private": true, "workspaces": ["web", "backend", "mobile", "admin", "shared"], "scripts": {"dev": "concurrently \"npm run dev:web\" \"npm run dev:backend\"", "dev:web": "cd web && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:admin": "cd admin && npm run dev", "build": "npm run build:web && npm run build:backend", "build:web": "cd web && npm run build", "build:backend": "cd backend && npm run build", "start": "npm run start:web && npm run start:backend", "start:web": "cd web && npm run start", "start:backend": "cd backend && npm run start", "lint": "npm run lint:web && npm run lint:backend", "lint:web": "cd web && npm run lint", "lint:backend": "cd backend && npm run lint", "test": "npm run test:web && npm run test:backend", "test:web": "cd web && npm run test", "test:backend": "cd backend && npm run test", "db:setup": "cd backend && npm run db:setup", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["algeria", "municipalities", "services", "directory", "local-services", "بلديات", "الجزائر", "خدمات"], "author": "Bldaty Team", "license": "MIT"}