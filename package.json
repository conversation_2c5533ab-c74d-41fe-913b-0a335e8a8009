{"name": "b<PERSON><PERSON>", "version": "1.0.0", "description": "دليل بلدتي - منصة شاملة لجميع بلديات الجزائر", "private": true, "workspaces": ["web", "backend-laravel", "mobile", "admin", "shared"], "scripts": {"dev": "concurrently \"npm run dev:web\" \"npm run dev:backend\"", "dev:web": "cd web && npm run dev", "dev:backend": "cd backend-laravel && php artisan serve", "dev:admin": "cd admin && npm run dev", "build": "npm run build:web", "build:web": "cd web && npm run build", "start": "npm run start:web", "start:web": "cd web && npm run start", "lint": "npm run lint:web", "lint:web": "cd web && npm run lint", "test": "npm run test:web && npm run test:backend", "test:web": "cd web && npm run test", "test:backend": "cd backend-laravel && php artisan test", "db:setup": "cd backend-laravel && php artisan migrate:fresh --seed", "db:migrate": "cd backend-laravel && php artisan migrate", "db:seed": "cd backend-laravel && php artisan db:seed", "laravel:install": "cd backend-laravel && composer install && php artisan key:generate", "laravel:serve": "cd backend-laravel && php artisan serve", "laravel:migrate": "cd backend-laravel && php artisan migrate", "laravel:seed": "cd backend-laravel && php artisan db:seed", "laravel:fresh": "cd backend-laravel && php artisan migrate:fresh --seed"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["algeria", "municipalities", "services", "directory", "local-services", "بلديات", "الجزائر", "خدمات"], "author": "Bldaty Team", "license": "MIT"}