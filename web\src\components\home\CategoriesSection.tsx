'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { 
  IdentificationIcon,
  HeartIcon,
  AcademicCapIcon,
  BuildingLibraryIcon,
  ShoppingBagIcon,
  TruckIcon,
  BuildingOfficeIcon,
  BoltIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline'

const categories = [
  {
    id: 'civil-status',
    icon: IdentificationIcon,
    title: 'الحالة المدنية',
    description: 'مكاتب الحالة المدنية، استخراج الوثائق، شهادات الميلاد والوفاة',
    count: '1,541',
    color: 'bg-blue-500',
    gradient: 'from-blue-500 to-blue-600'
  },
  {
    id: 'health',
    icon: HeartIcon,
    title: 'المراكز الصحية',
    description: 'مستوصفات، صيدليات، عيادات، مراكز الأمومة والطفولة',
    count: '2,847',
    color: 'bg-red-500',
    gradient: 'from-red-500 to-red-600'
  },
  {
    id: 'education',
    icon: AcademicCapIcon,
    title: 'التعليم',
    description: 'مدارس ابتدائية، متوسطات، ثانويات، جامعات، مراكز التكوين',
    count: '15,234',
    color: 'bg-green-500',
    gradient: 'from-green-500 to-green-600'
  },
  {
    id: 'mosques',
    icon: BuildingLibraryIcon,
    title: 'المساجد والزوايا',
    description: 'مساجد، زوايا، مدارس قرآنية، مراكز التعليم الديني',
    count: '8,567',
    color: 'bg-emerald-500',
    gradient: 'from-emerald-500 to-emerald-600'
  },
  {
    id: 'commerce',
    icon: ShoppingBagIcon,
    title: 'التجارة والخدمات',
    description: 'محلات، مقاهي، مطاعم، خدمات الحلاقة والخياطة',
    count: '25,891',
    color: 'bg-orange-500',
    gradient: 'from-orange-500 to-orange-600'
  },
  {
    id: 'transport',
    icon: TruckIcon,
    title: 'النقل',
    description: 'مواقف، محطات الحافلات، تاكسي، كراء السيارات',
    count: '3,456',
    color: 'bg-purple-500',
    gradient: 'from-purple-500 to-purple-600'
  },
  {
    id: 'administration',
    icon: BuildingOfficeIcon,
    title: 'الإدارة',
    description: 'بلديات، مصالح الضرائب، المحاكم، الضمان الاجتماعي',
    count: '4,123',
    color: 'bg-indigo-500',
    gradient: 'from-indigo-500 to-indigo-600'
  },
  {
    id: 'utilities',
    icon: BoltIcon,
    title: 'المصالح التقنية',
    description: 'سونلغاز، الجزائرية للمياه، اتصالات الجزائر، البريد',
    count: '2,789',
    color: 'bg-yellow-500',
    gradient: 'from-yellow-500 to-yellow-600'
  },
  {
    id: 'craftsmen',
    icon: WrenchScrewdriverIcon,
    title: 'الحرفيون',
    description: 'سباكة، كهرباء، بناء، ديكور، نجارة، حدادة',
    count: '12,345',
    color: 'bg-gray-500',
    gradient: 'from-gray-500 to-gray-600'
  }
]

export function CategoriesSection() {
  const t = useTranslations('categories')

  return (
    <section className="py-20 bg-white dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-arabic mb-4">
            فئات المرافق والخدمات
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto font-arabic leading-relaxed">
            استكشف جميع أنواع المرافق والخدمات المتوفرة في بلديتك مرتبة حسب الفئة
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <Link
              key={category.id}
              href={`/categories/${category.id}`}
              className="group block"
            >
              <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-gray-700 overflow-hidden relative">
                {/* Background Gradient */}
                <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${category.gradient} opacity-10 rounded-bl-full`}></div>
                
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${category.color} text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <category.icon className="w-6 h-6" />
                </div>

                {/* Content */}
                <div className="relative">
                  {/* Title and Count */}
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic">
                      {category.title}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium text-white rounded-full ${category.color}`}>
                      {category.count}
                    </span>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-300 font-arabic leading-relaxed text-sm">
                    {category.description}
                  </p>

                  {/* Arrow */}
                  <div className="flex items-center mt-4 text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform duration-300">
                    <span className="text-sm font-medium font-arabic">استكشف الفئة</span>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Bottom Stats */}
        <div className="mt-16 bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4 font-arabic">
              إحصائيات شاملة لجميع بلديات الجزائر
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-300 mb-2">75,000+</div>
                <div className="text-primary-100 font-arabic">إجمالي المرافق</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-300 mb-2">1,541</div>
                <div className="text-primary-100 font-arabic">بلدية مغطاة</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-300 mb-2">58</div>
                <div className="text-primary-100 font-arabic">ولاية</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-300 mb-2">24/7</div>
                <div className="text-primary-100 font-arabic">متاح دائماً</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
