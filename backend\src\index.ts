import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import 'express-async-errors'

import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { errorHandler } from '@/middleware/errorHandler'
import { notFoundHandler } from '@/middleware/notFoundHandler'
import { requestLogger } from '@/middleware/requestLogger'
import { validateRequest } from '@/middleware/validation'

// Import routes
import authRoutes from '@/routes/auth'
import facilitiesRoutes from '@/routes/facilities'
import categoriesRoutes from '@/routes/categories'
import wilayasRoutes from '@/routes/wilayas'
import municipalitiesRoutes from '@/routes/municipalities'
import reviewsRoutes from '@/routes/reviews'
import searchRoutes from '@/routes/search'
import uploadRoutes from '@/routes/upload'
import adminRoutes from '@/routes/admin'

const app = express()

// Trust proxy for rate limiting
app.set('trust proxy', 1)

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https://res.cloudinary.com", "https://images.unsplash.com"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", config.cors.origin],
    },
  },
}))

// CORS configuration
app.use(cors({
  origin: config.cors.origin.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}))

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    message: 'طلبات كثيرة جداً من هذا العنوان، يرجى المحاولة لاحقاً.'
  },
  standardHeaders: true,
  legacyHeaders: false,
})
app.use('/api/', limiter)

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Compression
app.use(compression())

// Logging
if (config.nodeEnv === 'development') {
  app.use(morgan('dev'))
} else {
  app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }))
}

app.use(requestLogger)

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.nodeEnv,
    version: process.env.npm_package_version || '1.0.0'
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/facilities', facilitiesRoutes)
app.use('/api/categories', categoriesRoutes)
app.use('/api/wilayas', wilayasRoutes)
app.use('/api/municipalities', municipalitiesRoutes)
app.use('/api/reviews', reviewsRoutes)
app.use('/api/search', searchRoutes)
app.use('/api/upload', uploadRoutes)
app.use('/api/admin', adminRoutes)

// API documentation
if (config.nodeEnv === 'development') {
  const swaggerJsdoc = require('swagger-jsdoc')
  const swaggerUi = require('swagger-ui-express')
  
  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Bldaty API',
        version: '1.0.0',
        description: 'API documentation for Bldaty - Algeria Municipalities Directory',
      },
      servers: [
        {
          url: `http://localhost:${config.port}`,
          description: 'Development server',
        },
      ],
    },
    apis: ['./src/routes/*.ts'],
  }
  
  const specs = swaggerJsdoc(options)
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs))
}

// Error handling middleware
app.use(notFoundHandler)
app.use(errorHandler)

// Start server
const server = app.listen(config.port, () => {
  logger.info(`🚀 Server running on port ${config.port}`)
  logger.info(`📚 Environment: ${config.nodeEnv}`)
  if (config.nodeEnv === 'development') {
    logger.info(`📖 API Documentation: http://localhost:${config.port}/api-docs`)
  }
})

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

export default app
