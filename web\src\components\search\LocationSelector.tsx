'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronDownIcon, MapPinIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface Location {
  id: string
  name: string
  type: 'wilaya' | 'municipality' | 'district'
  parent?: string
  coordinates?: [number, number]
}

interface LocationSelectorProps {
  value?: Location | null
  onChange?: (location: Location | null) => void
  placeholder?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

// Mock data for Algerian locations
const algerianLocations: Location[] = [
  // Wilayas
  { id: 'w01', name: 'الجزائر', type: 'wilaya', coordinates: [36.7538, 3.0588] },
  { id: 'w31', name: 'وهران', type: 'wilaya', coordinates: [35.6976, -0.6337] },
  { id: 'w25', name: 'قسنطينة', type: 'wilaya', coordinates: [36.3650, 6.6147] },
  { id: 'w23', name: 'عنابة', type: 'wilaya', coordinates: [36.9000, 7.7667] },
  { id: 'w19', name: 'سطيف', type: 'wilaya', coordinates: [36.1833, 5.4167] },
  { id: 'w13', name: 'تلمسان', type: 'wilaya', coordinates: [34.8833, -1.3167] },
  { id: 'w06', name: 'بجاية', type: 'wilaya', coordinates: [36.7525, 5.0767] },
  { id: 'w05', name: 'باتنة', type: 'wilaya', coordinates: [35.5667, 6.1833] },
  
  // Municipalities for Algiers
  { id: 'm01-01', name: 'الجزائر الوسطى', type: 'municipality', parent: 'w01' },
  { id: 'm01-02', name: 'سيدي أمحمد', type: 'municipality', parent: 'w01' },
  { id: 'm01-03', name: 'المدنية', type: 'municipality', parent: 'w01' },
  { id: 'm01-04', name: 'حسين داي', type: 'municipality', parent: 'w01' },
  { id: 'm01-05', name: 'باب الوادي', type: 'municipality', parent: 'w01' },
  { id: 'm01-06', name: 'القصبة', type: 'municipality', parent: 'w01' },
  { id: 'm01-07', name: 'بئر مراد رايس', type: 'municipality', parent: 'w01' },
  { id: 'm01-08', name: 'بئر خادم', type: 'municipality', parent: 'w01' },
  
  // Municipalities for Oran
  { id: 'm31-01', name: 'وهران', type: 'municipality', parent: 'w31' },
  { id: 'm31-02', name: 'السانيا', type: 'municipality', parent: 'w31' },
  { id: 'm31-03', name: 'بئر الجير', type: 'municipality', parent: 'w31' },
  { id: 'm31-04', name: 'عين الترك', type: 'municipality', parent: 'w31' },
  
  // Municipalities for Constantine
  { id: 'm25-01', name: 'قسنطينة', type: 'municipality', parent: 'w25' },
  { id: 'm25-02', name: 'الخروب', type: 'municipality', parent: 'w25' },
  { id: 'm25-03', name: 'عين عبيد', type: 'municipality', parent: 'w25' },
]

export function LocationSelector({
  value,
  onChange,
  placeholder = 'اختر الموقع',
  className = '',
  size = 'md'
}: LocationSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedWilaya, setSelectedWilaya] = useState<string | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const wilayas = algerianLocations.filter(loc => loc.type === 'wilaya')
  const municipalities = selectedWilaya 
    ? algerianLocations.filter(loc => loc.type === 'municipality' && loc.parent === selectedWilaya)
    : []

  const filteredWilayas = wilayas.filter(wilaya =>
    wilaya.name.includes(searchQuery)
  )

  const filteredMunicipalities = municipalities.filter(municipality =>
    municipality.name.includes(searchQuery)
  )

  const handleLocationSelect = (location: Location) => {
    onChange?.(location)
    setIsOpen(false)
    setSearchQuery('')
    
    if (location.type === 'wilaya') {
      setSelectedWilaya(location.id)
    }
  }

  const handleWilayaSelect = (wilaya: Location) => {
    setSelectedWilaya(wilaya.id)
    setSearchQuery('')
  }

  const clearSelection = () => {
    onChange?.(null)
    setSelectedWilaya(null)
    setSearchQuery('')
  }

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'wilaya':
        return '🏛️'
      case 'municipality':
        return '🏘️'
      case 'district':
        return '🏠'
      default:
        return '📍'
    }
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between px-4 border border-gray-300 dark:border-gray-600 
          rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent 
          bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 
          hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200
          ${sizeClasses[size]}
        `}
      >
        <div className="flex items-center gap-2">
          <MapPinIcon className="w-5 h-5 text-gray-400" />
          <span className={`font-arabic ${value ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}`}>
            {value ? value.name : placeholder}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {value && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                clearSelection()
              }}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
            >
              <XMarkIcon className="w-4 h-4 text-gray-400" />
            </button>
          )}
          <ChevronDownIcon className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="ابحث عن ولاية أو بلدية..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-arabic"
              dir="rtl"
            />
          </div>

          <div className="max-h-80 overflow-y-auto">
            {/* Wilayas Section */}
            {(!selectedWilaya || searchQuery) && (
              <div className="p-2">
                <div className="px-3 py-2 text-sm font-semibold text-gray-500 dark:text-gray-400 font-arabic">
                  الولايات
                </div>
                {filteredWilayas.map((wilaya) => (
                  <button
                    key={wilaya.id}
                    onClick={() => handleLocationSelect(wilaya)}
                    onMouseEnter={() => !searchQuery && handleWilayaSelect(wilaya)}
                    className="w-full flex items-center gap-3 px-3 py-3 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 font-arabic"
                  >
                    <span className="text-lg">{getLocationIcon(wilaya.type)}</span>
                    <div className="flex-1 text-right">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {wilaya.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        ولاية
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Municipalities Section */}
            {selectedWilaya && municipalities.length > 0 && (
              <div className="p-2 border-t border-gray-200 dark:border-gray-700">
                <div className="px-3 py-2 text-sm font-semibold text-gray-500 dark:text-gray-400 font-arabic">
                  البلديات
                </div>
                {filteredMunicipalities.map((municipality) => (
                  <button
                    key={municipality.id}
                    onClick={() => handleLocationSelect(municipality)}
                    className="w-full flex items-center gap-3 px-3 py-3 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 font-arabic"
                  >
                    <span className="text-lg">{getLocationIcon(municipality.type)}</span>
                    <div className="flex-1 text-right">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {municipality.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        بلدية
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* No Results */}
            {searchQuery && filteredWilayas.length === 0 && filteredMunicipalities.length === 0 && (
              <div className="p-6 text-center text-gray-500 dark:text-gray-400 font-arabic">
                <MapPinIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>لم يتم العثور على مواقع لـ "{searchQuery}"</p>
                <p className="text-sm mt-1">جرب كلمات مختلفة أو تحقق من الإملاء</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
