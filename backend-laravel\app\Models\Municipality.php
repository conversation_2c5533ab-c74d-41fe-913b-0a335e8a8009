<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Municipality extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name_ar',
        'name_fr',
        'name_en',
        'coordinates',
        'population',
        'area',
        'postal_code',
        'wilaya_id',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'coordinates' => 'array',
        'population' => 'integer',
        'area' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the wilaya that owns the municipality.
     */
    public function wilaya()
    {
        return $this->belongsTo(Wilaya::class);
    }

    /**
     * Get the districts for the municipality.
     */
    public function districts()
    {
        return $this->hasMany(District::class);
    }

    /**
     * Get the facilities for the municipality.
     */
    public function facilities()
    {
        return $this->hasMany(Facility::class);
    }

    /**
     * Get the active districts for the municipality.
     */
    public function activeDistricts()
    {
        return $this->districts()->where('is_active', true);
    }

    /**
     * Get the active facilities for the municipality.
     */
    public function activeFacilities()
    {
        return $this->facilities()->where('is_active', true);
    }

    /**
     * Scope a query to only include active municipalities.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by wilaya.
     */
    public function scopeByWilaya($query, $wilayaId)
    {
        return $query->where('wilaya_id', $wilayaId);
    }

    /**
     * Get the municipality's name in the specified language.
     */
    public function getName($locale = 'ar'): string
    {
        return match($locale) {
            'fr' => $this->name_fr ?? $this->name_ar,
            'en' => $this->name_en ?? $this->name_ar,
            default => $this->name_ar,
        };
    }

    /**
     * Get the total number of facilities in the municipality.
     */
    public function getFacilitiesCountAttribute(): int
    {
        return $this->facilities()->where('is_active', true)->count();
    }

    /**
     * Get the total number of districts in the municipality.
     */
    public function getDistrictsCountAttribute(): int
    {
        return $this->districts()->where('is_active', true)->count();
    }

    /**
     * Get the full location path (Wilaya > Municipality).
     */
    public function getFullLocationAttribute(): string
    {
        return $this->wilaya->name_ar . ' > ' . $this->name_ar;
    }
}
