<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Wilaya extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name_ar',
        'name_fr',
        'name_en',
        'coordinates',
        'population',
        'area',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'coordinates' => 'array',
        'population' => 'integer',
        'area' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the municipalities for the wilaya.
     */
    public function municipalities()
    {
        return $this->hasMany(Municipality::class);
    }

    /**
     * Get the facilities for the wilaya.
     */
    public function facilities()
    {
        return $this->hasMany(Facility::class);
    }

    /**
     * Get the active municipalities for the wilaya.
     */
    public function activeMunicipalities()
    {
        return $this->municipalities()->where('is_active', true);
    }

    /**
     * Get the active facilities for the wilaya.
     */
    public function activeFacilities()
    {
        return $this->facilities()->where('is_active', true);
    }

    /**
     * Scope a query to only include active wilayas.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the wilaya's name in the specified language.
     */
    public function getName($locale = 'ar'): string
    {
        return match($locale) {
            'fr' => $this->name_fr ?? $this->name_ar,
            'en' => $this->name_en ?? $this->name_ar,
            default => $this->name_ar,
        };
    }

    /**
     * Get the total number of facilities in the wilaya.
     */
    public function getFacilitiesCountAttribute(): int
    {
        return $this->facilities()->where('is_active', true)->count();
    }

    /**
     * Get the total number of municipalities in the wilaya.
     */
    public function getMunicipalitiesCountAttribute(): int
    {
        return $this->municipalities()->where('is_active', true)->count();
    }
}
