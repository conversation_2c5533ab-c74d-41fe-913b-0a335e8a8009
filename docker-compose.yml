version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgis/postgis:15-3.3
    container_name: bldaty_postgres
    environment:
      POSTGRES_DB: bldaty_db
      POSTGRES_USER: bldaty_user
      POSTGRES_PASSWORD: bldaty_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - bldaty_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: bldaty_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bldaty_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bldaty_backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************/bldaty_db
      - REDIS_URL=redis://redis:6379
      - PORT=3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - bldaty_network
    command: npm run dev

  # Web Frontend
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: bldaty_web
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./web:/app
      - /app/node_modules
    networks:
      - bldaty_network
    command: npm run dev

  # Admin Dashboard
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: bldaty_admin
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    ports:
      - "3002:3002"
    depends_on:
      - backend
    volumes:
      - ./admin:/app
      - /app/node_modules
    networks:
      - bldaty_network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  bldaty_network:
    driver: bridge
