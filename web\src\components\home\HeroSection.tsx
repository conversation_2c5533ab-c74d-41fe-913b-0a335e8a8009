'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { MagnifyingGlassIcon, MapPinIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline'
import { SearchBar } from '@/components/search/SearchBar'
import { LocationSelector } from '@/components/search/LocationSelector'

export function HeroSection() {
  const t = useTranslations('home')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLocation, setSelectedLocation] = useState(null)

  return (
    <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/patterns/hero-pattern.svg')] opacity-10"></div>
      
      {/* Algeria Flag Colors Accent */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-algeria-green via-algeria-white to-algeria-red"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="text-center">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold font-arabic mb-6 leading-tight">
            <span className="block">دليل بلدتي</span>
            <span className="block text-secondary-400 text-2xl md:text-3xl lg:text-4xl mt-2 font-medium">
              كل ما تحتاجه في بلديتك... في هاتفك
            </span>
          </h1>
          
          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto font-arabic leading-relaxed">
            اكتشف جميع المرافق والخدمات في بلديتك من الحالة المدنية إلى المراكز الصحية، 
            من المدارس إلى المساجد - دليل شامل لجميع بلديات الجزائر
          </p>
          
          {/* Search Section */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Search Input */}
                <div className="lg:col-span-2">
                  <SearchBar
                    value={searchQuery}
                    onChange={setSearchQuery}
                    placeholder="ابحث عن مرفق أو خدمة... (مثال: مستوصف، مدرسة، مسجد)"
                    className="w-full"
                  />
                </div>
                
                {/* Location Selector */}
                <div>
                  <LocationSelector
                    value={selectedLocation}
                    onChange={setSelectedLocation}
                    placeholder="اختر البلدية"
                    className="w-full"
                  />
                </div>
              </div>
              
              {/* Quick Search Buttons */}
              <div className="flex flex-wrap gap-2 mt-4 justify-center">
                {[
                  { label: 'الحالة المدنية', icon: BuildingOfficeIcon },
                  { label: 'المراكز الصحية', icon: BuildingOfficeIcon },
                  { label: 'المدارس', icon: BuildingOfficeIcon },
                  { label: 'المساجد', icon: BuildingOfficeIcon },
                  { label: 'الصيدليات', icon: BuildingOfficeIcon },
                ].map((item, index) => (
                  <button
                    key={index}
                    className="flex items-center gap-2 px-4 py-2 bg-white/20 hover:bg-white/30 rounded-full text-sm font-medium transition-colors duration-200 backdrop-blur-sm border border-white/20"
                  >
                    <item.icon className="w-4 h-4" />
                    {item.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {[
              { number: '58', label: 'ولاية' },
              { number: '1,541', label: 'بلدية' },
              { number: '10,000+', label: 'مرفق وخدمة' },
              { number: '24/7', label: 'متاح دائماً' },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-primary-100 font-arabic">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 w-full">
        <svg
          className="w-full h-12 md:h-20 text-white"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="currentColor"
          ></path>
        </svg>
      </div>
    </section>
  )
}
