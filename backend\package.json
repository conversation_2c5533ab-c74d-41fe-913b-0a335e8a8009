{"name": "bldaty-backend", "version": "1.0.0", "description": "دليل بلدتي - الخ<PERSON>م الخلفي", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:setup": "npm run db:generate && npm run db:push && npm run db:seed"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "redis": "^4.6.10", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-async-errors": "^3.1.1", "joi": "^17.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "node-cron": "^3.0.3", "axios": "^1.6.2", "uuid": "^9.0.1", "slugify": "^1.6.6", "moment": "^2.29.4", "moment-timezone": "^0.5.43"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["algeria", "municipalities", "services", "api", "backend", "بلديات", "الجزائر", "خدمات"], "author": "Bldaty Team", "license": "MIT"}