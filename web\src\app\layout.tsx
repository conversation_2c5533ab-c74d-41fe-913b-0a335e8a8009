import type { Metada<PERSON> } from 'next'
import { Inter, Noto_Sans_Arabic } from 'next/font/google'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { QueryProvider } from '@/components/providers/QueryProvider'
import { AuthProvider } from '@/components/providers/AuthProvider'
import { ToastProvider } from '@/components/providers/ToastProvider'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const notoSansArabic = Noto_Sans_Arabic({
  subsets: ['arabic'],
  variable: '--font-arabic',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'دليل بلدتي - كل ما تحتاجه في بلديتك',
    template: '%s | دليل بلدتي'
  },
  description: 'منصة شاملة لجميع بلديات الجزائر - اكتشف المرافق والخدمات في بلديتك',
  keywords: [
    'الجزائر',
    'بلديات',
    'خدمات',
    'مرافق',
    'دليل',
    'Algeria',
    'municipalities',
    'services',
    'directory'
  ],
  authors: [{ name: 'Bldaty Team' }],
  creator: 'Bldaty',
  publisher: 'Bldaty',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'ar': '/ar',
      'fr': '/fr',
      'ar-DZ': '/ar-dz',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'ar_DZ',
    url: process.env.NEXT_PUBLIC_WEB_URL,
    title: 'دليل بلدتي - كل ما تحتاجه في بلديتك',
    description: 'منصة شاملة لجميع بلديات الجزائر - اكتشف المرافق والخدمات في بلديتك',
    siteName: 'دليل بلدتي',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'دليل بلدتي',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'دليل بلدتي - كل ما تحتاجه في بلديتك',
    description: 'منصة شاملة لجميع بلديات الجزائر - اكتشف المرافق والخدمات في بلديتك',
    images: ['/og-image.jpg'],
    creator: '@BldatyDZ',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

interface RootLayoutProps {
  children: React.ReactNode
  params: { locale: string }
}

export default async function RootLayout({
  children,
  params: { locale }
}: RootLayoutProps) {
  const messages = await getMessages()

  return (
    <html lang={locale} dir={locale === 'ar' || locale === 'ar-dz' ? 'rtl' : 'ltr'}>
      <body className={`${inter.variable} ${notoSansArabic.variable} font-latin antialiased`}>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider>
            <QueryProvider>
              <AuthProvider>
                <div className="min-h-screen flex flex-col">
                  <Header />
                  <main className="flex-1">
                    {children}
                  </main>
                  <Footer />
                </div>
                <ToastProvider />
              </AuthProvider>
            </QueryProvider>
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
