<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('municipalities', function (Blueprint $table) {
            $table->id();
            $table->string('code', 4)->unique()->comment('Municipality code (0101-5829)');
            $table->string('name_ar')->comment('Arabic name');
            $table->string('name_fr')->nullable()->comment('French name');
            $table->string('name_en')->nullable()->comment('English name');
            $table->json('coordinates')->nullable()->comment('Latitude and longitude');
            $table->unsignedInteger('population')->nullable()->comment('Population count');
            $table->decimal('area', 10, 2)->nullable()->comment('Area in km²');
            $table->string('postal_code', 5)->nullable()->comment('Postal code');
            $table->foreignId('wilaya_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['wilaya_id', 'is_active']);
            $table->index(['code']);
            $table->index(['postal_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('municipalities');
    }
};
