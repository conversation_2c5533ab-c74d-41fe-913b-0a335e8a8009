<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;
use Spatie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Facility extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, Searchable, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_fr',
        'name_en',
        'description_ar',
        'description_fr',
        'description_en',
        'address',
        'coordinates',
        'phone',
        'email',
        'website',
        'facebook_page',
        'working_hours',
        'is_open_24h',
        'is_active',
        'is_verified',
        'verified_at',
        'view_count',
        'average_rating',
        'total_reviews',
        'wilaya_id',
        'municipality_id',
        'district_id',
        'category_id',
        'subcategory_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'coordinates' => 'array',
        'working_hours' => 'array',
        'is_open_24h' => 'boolean',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'view_count' => 'integer',
        'average_rating' => 'float',
        'total_reviews' => 'integer',
    ];

    /**
     * Get the wilaya that owns the facility.
     */
    public function wilaya()
    {
        return $this->belongsTo(Wilaya::class);
    }

    /**
     * Get the municipality that owns the facility.
     */
    public function municipality()
    {
        return $this->belongsTo(Municipality::class);
    }

    /**
     * Get the district that owns the facility.
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Get the category that owns the facility.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the subcategory that owns the facility.
     */
    public function subcategory()
    {
        return $this->belongsTo(Subcategory::class);
    }

    /**
     * Get the reviews for the facility.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the approved reviews for the facility.
     */
    public function approvedReviews()
    {
        return $this->reviews()->where('is_approved', true);
    }

    /**
     * Get the working hours for the facility.
     */
    public function workingHoursDetails()
    {
        return $this->hasMany(WorkingHours::class);
    }

    /**
     * Get the users who favorited this facility.
     */
    public function favoritedBy()
    {
        return $this->belongsToMany(User::class, 'user_favorites')
                    ->withTimestamps();
    }

    /**
     * Get the reports for the facility.
     */
    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Scope a query to only include active facilities.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include verified facilities.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope a query to filter by wilaya.
     */
    public function scopeByWilaya($query, $wilayaId)
    {
        return $query->where('wilaya_id', $wilayaId);
    }

    /**
     * Scope a query to filter by municipality.
     */
    public function scopeByMunicipality($query, $municipalityId)
    {
        return $query->where('municipality_id', $municipalityId);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get the facility's name in the specified language.
     */
    public function getName($locale = 'ar'): string
    {
        return match($locale) {
            'fr' => $this->name_fr ?? $this->name_ar,
            'en' => $this->name_en ?? $this->name_ar,
            default => $this->name_ar,
        };
    }

    /**
     * Get the facility's description in the specified language.
     */
    public function getDescription($locale = 'ar'): ?string
    {
        return match($locale) {
            'fr' => $this->description_fr ?? $this->description_ar,
            'en' => $this->description_en ?? $this->description_ar,
            default => $this->description_ar,
        };
    }

    /**
     * Get the full location path.
     */
    public function getFullLocationAttribute(): string
    {
        $location = $this->wilaya->name_ar . ' > ' . $this->municipality->name_ar;
        
        if ($this->district) {
            $location .= ' > ' . $this->district->name_ar;
        }
        
        return $location;
    }

    /**
     * Get the main image URL.
     */
    public function getMainImageUrlAttribute(): ?string
    {
        return $this->getFirstMediaUrl('images');
    }

    /**
     * Get all image URLs.
     */
    public function getImageUrlsAttribute(): array
    {
        return $this->getMedia('images')->map(function ($media) {
            return $media->getUrl();
        })->toArray();
    }

    /**
     * Increment the view count.
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Update the average rating.
     */
    public function updateAverageRating(): void
    {
        $averageRating = $this->approvedReviews()->avg('rating') ?? 0;
        $totalReviews = $this->approvedReviews()->count();
        
        $this->update([
            'average_rating' => round($averageRating, 2),
            'total_reviews' => $totalReviews,
        ]);
    }

    /**
     * Get the indexable data array for the model.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name_ar' => $this->name_ar,
            'name_fr' => $this->name_fr,
            'name_en' => $this->name_en,
            'description_ar' => $this->description_ar,
            'address' => $this->address,
            'wilaya' => $this->wilaya->name_ar,
            'municipality' => $this->municipality->name_ar,
            'category' => $this->category->name_ar,
            'subcategory' => $this->subcategory?->name_ar,
            'coordinates' => $this->coordinates,
            'average_rating' => $this->average_rating,
            'is_verified' => $this->is_verified,
        ];
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
              ->width(300)
              ->height(300)
              ->sharpen(10);

        $this->addMediaConversion('preview')
              ->width(800)
              ->height(600)
              ->quality(90);
    }
}
