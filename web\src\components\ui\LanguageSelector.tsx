'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline'

const languages = [
  {
    code: 'ar',
    name: 'العربية',
    nativeName: 'العربية الفصحى',
    flag: '🇩🇿',
    dir: 'rtl'
  },
  {
    code: 'ar-dz',
    name: 'الدارجة',
    nativeName: 'الدارجة الجزائرية',
    flag: '🇩🇿',
    dir: 'rtl'
  },
  {
    code: 'fr',
    name: 'Français',
    nativeName: 'Français',
    flag: '🇫🇷',
    dir: 'ltr'
  }
]

export function LanguageSelector() {
  const [isOpen, setIsOpen] = useState(false)
  const [currentLang, setCurrentLang] = useState('ar')
  const containerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Get current language from pathname or default to 'ar'
    const pathLang = pathname.split('/')[1]
    const validLang = languages.find(lang => lang.code === pathLang)
    setCurrentLang(validLang?.code || 'ar')
  }, [pathname])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLanguageChange = (langCode: string) => {
    setCurrentLang(langCode)
    setIsOpen(false)
    
    // Update the URL with the new language
    const segments = pathname.split('/')
    const currentLangInPath = languages.find(lang => lang.code === segments[1])
    
    let newPath: string
    if (currentLangInPath) {
      // Replace existing language in path
      segments[1] = langCode
      newPath = segments.join('/')
    } else {
      // Add language to path
      newPath = `/${langCode}${pathname}`
    }
    
    router.push(newPath)
  }

  const currentLanguage = languages.find(lang => lang.code === currentLang) || languages[0]

  return (
    <div ref={containerRef} className="relative">
      {/* Language Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
        aria-label="اختيار اللغة"
      >
        <span className="text-lg">{currentLanguage.flag}</span>
        <span className="hidden sm:block font-medium">
          {currentLanguage.name}
        </span>
        <ChevronDownIcon className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-48 animate-slide-down">
          <div className="p-2">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`
                  w-full flex items-center gap-3 px-3 py-3 text-right rounded-lg transition-colors duration-150
                  ${language.code === currentLang 
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' 
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
                dir={language.dir}
              >
                <span className="text-xl">{language.flag}</span>
                <div className="flex-1 text-right">
                  <div className="font-medium">
                    {language.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {language.nativeName}
                  </div>
                </div>
                {language.code === currentLang && (
                  <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
          
          {/* Language Info */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-3">
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <GlobeAltIcon className="w-4 h-4" />
              <span className="font-arabic">اللغة المحددة: {currentLanguage.nativeName}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
