# 🏛️ دليل بلدتي - Bldaty

**منصة شاملة لجميع بلديات الجزائر - كل ما تحتاجه في بلديتك... في هاتفك**

## 📖 نظرة عامة

دليل بلدتي هو منصة إلكترونية وتطبيق موبايل شامل يغطي جميع بلديات الجزائر (1541 بلدية)، يتيح للمواطنين البحث والعثور على جميع المرافق والخدمات المحلية المتوفرة في بلدياتهم بكل سهولة وتفاعلية.

## ✨ المميزات الرئيسية

### 🏢 فئات المرافق والخدمات
- 📍 **الحالة المدنية** - مكاتب الحالة المدنية والوثائق
- 🏥 **المراكز الصحية** - مستوصفات، صيدليات، عيادات
- 🏫 **التعليم** - مدارس، ثانويات، جامعات، مراكز تكوين
- 🕌 **المساجد والزوايا** - أماكن العبادة والتعليم الديني
- 🛍️ **التجارة والخدمات** - محلات، مقاهي، مطاعم، خدمات
- 🚌 **النقل** - مواقف، محطات، تاكسي، كراء سيارات
- 🏛️ **الإدارة** - بلدية، ضرائب، عدالة، ضمان اجتماعي
- 💡 **المصالح التقنية** - سونلغاز، المياه، الاتصالات
- 🛠️ **الحرفيون** - سباكة، كهرباء، بناء، ديكور

### 🔍 مميزات البحث والاستكشاف
- البحث السريع حسب الاسم، النوع، أو الموقع
- عرض تفاعلي على الخريطة
- فلترة حسب البلدية، الولاية، أو الحي
- البحث الصوتي باللهجة الجزائرية
- اقتراحات ذكية حسب الموقع

### 📱 مميزات المستخدم
- صور واقعية للمرافق
- مواعيد العمل وحالة الفتح/الإغلاق
- معلومات الاتصال الكاملة
- تقييمات وتعليقات المواطنين
- نظام المفضلة
- الإبلاغ عن أخطاء أو تحديثات
- إشعارات الخدمات الجديدة

### 🌐 الدعم متعدد اللغات
- **العربية الفصحى**
- **اللهجة الجزائرية** (الدارجة)
- **الفرنسية**

## 🏗️ البنية التقنية

### Frontend
- **Web**: Next.js 14 مع TypeScript
- **Mobile**: React Native مع Expo
- **Admin**: React مع Material-UI

### Backend
- **API**: Laravel 10 مع PHP 8.1+
- **Database**: MySQL/PostgreSQL
- **ORM**: Eloquent
- **Cache**: Redis
- **Search**: Laravel Scout + Algolia

### الخدمات الخارجية
- **Maps**: Google Maps API + OpenStreetMap
- **Images**: Cloudinary
- **Auth**: NextAuth.js
- **Push Notifications**: Firebase

## 🚀 التشغيل السريع

### المتطلبات
- Node.js 18+ (للواجهة الأمامية)
- PHP 8.1+ و Composer (للخادم الخلفي)
- MySQL 8.0+ أو PostgreSQL 13+
- Redis

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/bldaty.git
cd bldaty
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة القيم المطلوبة
```

4. **إعداد الخادم الخلفي (Laravel)**
```bash
# تثبيت تبعيات Laravel
npm run laravel:install

# إعداد قاعدة البيانات
npm run db:setup
```

5. **تشغيل التطبيق**
```bash
# تشغيل الواجهة الأمامية والخادم الخلفي معاً
npm run dev

# أو تشغيلهما منفصلين
npm run dev:web      # الواجهة الأمامية على المنفذ 3000
npm run dev:backend  # الخادم الخلفي على المنفذ 8000
```

### الوصول للتطبيق
- **الموقع الرئيسي**: http://localhost:3000
- **API Laravel**: http://localhost:8000
- **لوحة التحكم**: http://localhost:3002

## 📊 هيكل المشروع

```
bldaty/
├── web/                    # منصة الويب (Next.js)
├── mobile/                 # تطبيق الموبايل (React Native)
├── backend-laravel/        # الخادم الخلفي (Laravel)
├── admin/                 # لوحة التحكم
├── shared/                # الكود والأنواع المشتركة
├── docs/                  # الوثائق
└── docker-compose.yml     # إعداد Docker
```

## 🗺️ تغطية جغرافية

المنصة تغطي جميع ولايات الجزائر الـ58:
- من العاصمة إلى تمنراست
- من وهران إلى تبسة
- جميع البلديات الـ1541
- تنظيم حسب الأحياء والمناطق

## 💰 النموذج الربحي

- اشتراكات مميزة للمرافق
- إعلانات محلية مستهدفة
- خدمات premium للمحلات
- بيع البيانات التحليلية للبلديات

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للمزيد من التفاصيل.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع**: https://bldaty.dz
- **البريد الإلكتروني**: <EMAIL>
- **فيسبوك**: @BldatyDZ
- **تويتر**: @BldatyDZ

---

**صنع بـ ❤️ للجزائر وأهلها**
