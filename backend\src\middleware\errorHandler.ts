import { Request, Response, NextFunction } from 'express'
import { Prisma } from '@prisma/client'
import { logger } from '@/utils/logger'

export interface AppError extends Error {
  statusCode?: number
  isOperational?: boolean
}

export class CustomError extends Error implements AppError {
  statusCode: number
  isOperational: boolean

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

export const createError = (message: string, statusCode: number = 500): CustomError => {
  return new CustomError(message, statusCode)
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = error.statusCode || 500
  let message = error.message || 'Internal Server Error'
  let details: any = null

  // Log the error
  logger.error('Error occurred', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  })

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        statusCode = 409
        message = 'A record with this information already exists'
        details = {
          field: error.meta?.target,
          code: error.code
        }
        break
      case 'P2025':
        statusCode = 404
        message = 'Record not found'
        details = {
          code: error.code
        }
        break
      case 'P2003':
        statusCode = 400
        message = 'Foreign key constraint failed'
        details = {
          field: error.meta?.field_name,
          code: error.code
        }
        break
      default:
        statusCode = 400
        message = 'Database operation failed'
        details = {
          code: error.code
        }
    }
  }

  // Handle Prisma validation errors
  if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400
    message = 'Invalid data provided'
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    message = 'Invalid token'
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401
    message = 'Token expired'
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    statusCode = 400
    message = 'Validation failed'
  }

  // Handle multer errors
  if (error.name === 'MulterError') {
    statusCode = 400
    switch ((error as any).code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File too large'
        break
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files'
        break
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field'
        break
      default:
        message = 'File upload error'
    }
  }

  // Prepare error response
  const errorResponse: any = {
    success: false,
    error: {
      message,
      statusCode,
    }
  }

  // Add details in development mode
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = details
    errorResponse.error.stack = error.stack
  }

  // Add Arabic translation for common errors
  const arabicMessages: Record<string, string> = {
    'A record with this information already exists': 'سجل بهذه المعلومات موجود بالفعل',
    'Record not found': 'السجل غير موجود',
    'Invalid token': 'رمز غير صالح',
    'Token expired': 'انتهت صلاحية الرمز',
    'Validation failed': 'فشل في التحقق من البيانات',
    'File too large': 'الملف كبير جداً',
    'Too many files': 'ملفات كثيرة جداً',
    'Unauthorized': 'غير مخول',
    'Forbidden': 'ممنوع',
    'Internal Server Error': 'خطأ داخلي في الخادم'
  }

  if (arabicMessages[message]) {
    errorResponse.error.messageAr = arabicMessages[message]
  }

  res.status(statusCode).json(errorResponse)
}

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Not found handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new CustomError(`Route ${req.originalUrl} not found`, 404)
  next(error)
}
