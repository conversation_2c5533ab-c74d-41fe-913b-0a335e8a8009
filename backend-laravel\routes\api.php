<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\FacilityController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\WilayaController;
use App\Http\Controllers\Api\MunicipalityController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\SearchController;
use App\Http\Controllers\Api\FavoriteController;
use App\Http\Controllers\Api\ReportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::post('verify-email', [AuthController::class, 'verifyEmail']);
        Route::post('resend-verification', [AuthController::class, 'resendVerification']);
    });

    // Public facility routes
    Route::prefix('facilities')->group(function () {
        Route::get('/', [FacilityController::class, 'index']);
        Route::get('/{facility}', [FacilityController::class, 'show']);
        Route::get('/location/nearby', [FacilityController::class, 'byLocation']);
        Route::get('/popular', [FacilityController::class, 'popular']);
        Route::get('/recent', [FacilityController::class, 'recent']);
    });

    // Categories routes
    Route::prefix('categories')->group(function () {
        Route::get('/', [CategoryController::class, 'index']);
        Route::get('/{category}', [CategoryController::class, 'show']);
        Route::get('/{category}/facilities', [CategoryController::class, 'facilities']);
        Route::get('/{category}/subcategories', [CategoryController::class, 'subcategories']);
    });

    // Wilayas routes
    Route::prefix('wilayas')->group(function () {
        Route::get('/', [WilayaController::class, 'index']);
        Route::get('/{wilaya}', [WilayaController::class, 'show']);
        Route::get('/{wilaya}/municipalities', [WilayaController::class, 'municipalities']);
        Route::get('/{wilaya}/facilities', [WilayaController::class, 'facilities']);
    });

    // Municipalities routes
    Route::prefix('municipalities')->group(function () {
        Route::get('/', [MunicipalityController::class, 'index']);
        Route::get('/{municipality}', [MunicipalityController::class, 'show']);
        Route::get('/{municipality}/facilities', [MunicipalityController::class, 'facilities']);
    });

    // Search routes
    Route::prefix('search')->group(function () {
        Route::get('/', [SearchController::class, 'search']);
        Route::get('/suggestions', [SearchController::class, 'suggestions']);
        Route::get('/autocomplete', [SearchController::class, 'autocomplete']);
    });

    // Public reviews (read-only)
    Route::get('facilities/{facility}/reviews', [ReviewController::class, 'index']);

});

// Protected routes (require authentication)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    
    // User authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
    });

    // User routes
    Route::prefix('user')->group(function () {
        Route::get('profile', [UserController::class, 'profile']);
        Route::put('profile', [UserController::class, 'updateProfile']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::delete('avatar', [UserController::class, 'deleteAvatar']);
    });

    // User favorites
    Route::prefix('favorites')->group(function () {
        Route::get('/', [FavoriteController::class, 'index']);
        Route::post('/{facility}', [FavoriteController::class, 'store']);
        Route::delete('/{facility}', [FavoriteController::class, 'destroy']);
        Route::get('/check/{facility}', [FavoriteController::class, 'check']);
    });

    // Reviews (authenticated users can create/update/delete)
    Route::prefix('reviews')->group(function () {
        Route::post('/', [ReviewController::class, 'store']);
        Route::put('/{review}', [ReviewController::class, 'update']);
        Route::delete('/{review}', [ReviewController::class, 'destroy']);
        Route::get('/my-reviews', [ReviewController::class, 'myReviews']);
    });

    // Reports
    Route::prefix('reports')->group(function () {
        Route::post('/', [ReportController::class, 'store']);
        Route::get('/my-reports', [ReportController::class, 'myReports']);
    });

    // Facility management (for verified users/moderators)
    Route::middleware('role:moderator|admin')->group(function () {
        Route::post('facilities', [FacilityController::class, 'store']);
        Route::put('facilities/{facility}', [FacilityController::class, 'update']);
        Route::delete('facilities/{facility}', [FacilityController::class, 'destroy']);
    });

});

// Admin routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role:admin'])->group(function () {
    
    // Admin facility management
    Route::prefix('facilities')->group(function () {
        Route::get('/', [FacilityController::class, 'adminIndex']);
        Route::post('/{facility}/verify', [FacilityController::class, 'verify']);
        Route::post('/{facility}/unverify', [FacilityController::class, 'unverify']);
        Route::get('/pending', [FacilityController::class, 'pending']);
    });

    // Admin review management
    Route::prefix('reviews')->group(function () {
        Route::get('/pending', [ReviewController::class, 'pending']);
        Route::post('/{review}/approve', [ReviewController::class, 'approve']);
        Route::post('/{review}/reject', [ReviewController::class, 'reject']);
    });

    // Admin report management
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index']);
        Route::get('/{report}', [ReportController::class, 'show']);
        Route::put('/{report}', [ReportController::class, 'update']);
        Route::post('/{report}/resolve', [ReportController::class, 'resolve']);
    });

    // Admin user management
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index']);
        Route::get('/{user}', [UserController::class, 'show']);
        Route::put('/{user}', [UserController::class, 'update']);
        Route::post('/{user}/ban', [UserController::class, 'ban']);
        Route::post('/{user}/unban', [UserController::class, 'unban']);
    });

    // Admin statistics
    Route::get('statistics', [AdminController::class, 'statistics']);
    Route::get('analytics', [AdminController::class, 'analytics']);

});

// Health check route
Route::get('health', function () {
    return response()->json([
        'status' => 'OK',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version', '1.0.0'),
        'environment' => app()->environment(),
    ]);
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found',
        'error' => 'The requested API endpoint does not exist.'
    ], 404);
});
