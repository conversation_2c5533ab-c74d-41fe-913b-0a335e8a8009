'use client'

import { useTranslations } from 'next-intl'
import { 
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  StarIcon,
  HeartIcon,
  ExclamationTriangleIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

const features = [
  {
    icon: MagnifyingGlassIcon,
    title: 'البحث السريع والذكي',
    description: 'ابحث عن أي مرفق أو خدمة بالاسم، النوع، أو الموقع مع اقتراحات ذكية',
    color: 'text-blue-600'
  },
  {
    icon: MapPinIcon,
    title: 'خرائط تفاعلية',
    description: 'اعرض جميع المرافق على خريطة تفاعلية مع إمكانية التنقل والاتجاهات',
    color: 'text-green-600'
  },
  {
    icon: ClockIcon,
    title: 'مواعيد العمل المحدثة',
    description: 'تحقق من مواعيد العمل وحالة الفتح/الإغلاق في الوقت الفعلي',
    color: 'text-orange-600'
  },
  {
    icon: StarIcon,
    title: 'تقييمات وتعليقات',
    description: 'اقرأ تقييمات المواطنين وشارك تجربتك مع الآخرين',
    color: 'text-yellow-600'
  },
  {
    icon: HeartIcon,
    title: 'قائمة المفضلة',
    description: 'احفظ أماكنك المفضلة للوصول السريع إليها لاحقاً',
    color: 'text-red-600'
  },
  {
    icon: ExclamationTriangleIcon,
    title: 'الإبلاغ عن الأخطاء',
    description: 'ساعدنا في تحديث البيانات من خلال الإبلاغ عن أي معلومات خاطئة',
    color: 'text-purple-600'
  },
  {
    icon: DevicePhoneMobileIcon,
    title: 'تطبيق موبايل',
    description: 'استخدم التطبيق على هاتفك الذكي مع إشعارات فورية',
    color: 'text-indigo-600'
  },
  {
    icon: GlobeAltIcon,
    title: 'متعدد اللغات',
    description: 'متاح بالعربية، الفرنسية، واللهجة الجزائرية',
    color: 'text-teal-600'
  }
]

export function FeaturesSection() {
  const t = useTranslations('features')

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-arabic mb-4">
            مميزات منصة دليل بلدتي
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto font-arabic leading-relaxed">
            نوفر لك جميع الأدوات والمعلومات التي تحتاجها للعثور على المرافق والخدمات في بلديتك بسهولة وسرعة
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-gray-700"
            >
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className={`w-6 h-6 ${feature.color}`} />
              </div>

              {/* Title */}
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 font-arabic">
                {feature.title}
              </h3>

              {/* Description */}
              <p className="text-gray-600 dark:text-gray-300 font-arabic leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-soft border border-gray-200 dark:border-gray-700 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 font-arabic">
              جرب جميع المميزات مجاناً
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 font-arabic">
              ابدأ في استكشاف بلديتك الآن واكتشف جميع المرافق والخدمات المتاحة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary px-8 py-3 text-lg font-arabic">
                ابدأ الاستكشاف
              </button>
              <button className="btn-outline px-8 py-3 text-lg font-arabic">
                حمل التطبيق
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
