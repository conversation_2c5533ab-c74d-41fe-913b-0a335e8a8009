// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and user management
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String?  @unique
  firstName   String?
  lastName    String?
  phone       String?
  avatar      String?
  role        UserRole @default(USER)
  isActive    Boolean  @default(true)
  isVerified  Boolean  @default(false)
  lastLoginAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  reviews     Review[]
  favorites   UserFavorite[]
  reports     Report[]
  
  @@map("users")
}

enum UserRole {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

// Wilaya (Province) model
model Wilaya {
  id           String  @id @default(cuid())
  code         String  @unique // e.g., "01", "31"
  nameAr       String
  nameFr       String?
  nameEn       String?
  coordinates  Json?   // [latitude, longitude]
  population   Int?
  area         Float?  // in km²
  isActive     Boolean @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  municipalities Municipality[]
  facilities     Facility[]
  
  @@map("wilayas")
}

// Municipality (Commune) model
model Municipality {
  id          String  @id @default(cuid())
  code        String  @unique // e.g., "0101", "3101"
  nameAr      String
  nameFr      String?
  nameEn      String?
  coordinates Json?   // [latitude, longitude]
  population  Int?
  area        Float?  // in km²
  postalCode  String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  wilayaId    String
  wilaya      Wilaya @relation(fields: [wilayaId], references: [id])

  // Relations
  facilities  Facility[]
  districts   District[]
  
  @@map("municipalities")
}

// District (Quartier/Hay) model
model District {
  id             String  @id @default(cuid())
  nameAr         String
  nameFr         String?
  nameEn         String?
  coordinates    Json?   // [latitude, longitude]
  isActive       Boolean @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Foreign keys
  municipalityId String
  municipality   Municipality @relation(fields: [municipalityId], references: [id])

  // Relations
  facilities     Facility[]
  
  @@map("districts")
}

// Category model for facility types
model Category {
  id          String  @id @default(cuid())
  slug        String  @unique
  nameAr      String
  nameFr      String?
  nameEn      String?
  descriptionAr String?
  descriptionFr String?
  descriptionEn String?
  icon        String?
  color       String?
  sortOrder   Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  subcategories Subcategory[]
  facilities    Facility[]
  
  @@map("categories")
}

// Subcategory model
model Subcategory {
  id          String  @id @default(cuid())
  slug        String  @unique
  nameAr      String
  nameFr      String?
  nameEn      String?
  descriptionAr String?
  descriptionFr String?
  descriptionEn String?
  icon        String?
  sortOrder   Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])

  // Relations
  facilities  Facility[]
  
  @@map("subcategories")
}

// Main Facility model
model Facility {
  id              String   @id @default(cuid())
  nameAr          String
  nameFr          String?
  nameEn          String?
  descriptionAr   String?
  descriptionFr   String?
  descriptionEn   String?
  address         String?
  coordinates     Json?    // [latitude, longitude]
  phone           String?
  email           String?
  website         String?
  facebookPage    String?
  workingHours    Json?    // Working hours structure
  isOpen24h       Boolean  @default(false)
  isActive        Boolean  @default(true)
  isVerified      Boolean  @default(false)
  verifiedAt      DateTime?
  viewCount       Int      @default(0)
  averageRating   Float    @default(0)
  totalReviews    Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Foreign keys
  wilayaId        String
  wilaya          Wilaya @relation(fields: [wilayaId], references: [id])
  
  municipalityId  String
  municipality    Municipality @relation(fields: [municipalityId], references: [id])
  
  districtId      String?
  district        District? @relation(fields: [districtId], references: [id])
  
  categoryId      String
  category        Category @relation(fields: [categoryId], references: [id])
  
  subcategoryId   String?
  subcategory     Subcategory? @relation(fields: [subcategoryId], references: [id])

  // Relations
  images          FacilityImage[]
  reviews         Review[]
  favorites       UserFavorite[]
  reports         Report[]
  workingHoursDetails WorkingHours[]
  
  @@map("facilities")
}

// Facility images model
model FacilityImage {
  id          String   @id @default(cuid())
  url         String
  publicId    String?  // Cloudinary public ID
  alt         String?
  caption     String?
  sortOrder   Int      @default(0)
  isMain      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  facilityId  String
  facility    Facility @relation(fields: [facilityId], references: [id], onDelete: Cascade)
  
  @@map("facility_images")
}

// Working hours model
model WorkingHours {
  id          String   @id @default(cuid())
  dayOfWeek   Int      // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  openTime    String?  // e.g., "08:00"
  closeTime   String?  // e.g., "17:00"
  isClosed    Boolean  @default(false)
  isBreak     Boolean  @default(false)
  breakStart  String?  // e.g., "12:00"
  breakEnd    String?  // e.g., "13:00"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  facilityId  String
  facility    Facility @relation(fields: [facilityId], references: [id], onDelete: Cascade)
  
  @@unique([facilityId, dayOfWeek])
  @@map("working_hours")
}

// Review model
model Review {
  id          String   @id @default(cuid())
  rating      Int      // 1-5 stars
  title       String?
  comment     String?
  isAnonymous Boolean  @default(false)
  isApproved  Boolean  @default(false)
  approvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  userId      String
  user        User @relation(fields: [userId], references: [id])
  
  facilityId  String
  facility    Facility @relation(fields: [facilityId], references: [id], onDelete: Cascade)
  
  @@unique([userId, facilityId])
  @@map("reviews")
}

// User favorites model
model UserFavorite {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())

  // Foreign keys
  userId      String
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  facilityId  String
  facility    Facility @relation(fields: [facilityId], references: [id], onDelete: Cascade)
  
  @@unique([userId, facilityId])
  @@map("user_favorites")
}

// Report model for user-generated content
model Report {
  id          String     @id @default(cuid())
  type        ReportType
  title       String
  description String
  status      ReportStatus @default(PENDING)
  response    String?
  respondedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Foreign keys
  userId      String
  user        User @relation(fields: [userId], references: [id])
  
  facilityId  String?
  facility    Facility? @relation(fields: [facilityId], references: [id])
  
  @@map("reports")
}

enum ReportType {
  INCORRECT_INFO
  CLOSED_PERMANENTLY
  WRONG_LOCATION
  INAPPROPRIATE_CONTENT
  DUPLICATE
  OTHER
}

enum ReportStatus {
  PENDING
  IN_REVIEW
  RESOLVED
  REJECTED
}
