'use client'

import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { 
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline'

const footerLinks = {
  platform: [
    { name: 'الرئيسية', href: '/' },
    { name: 'الفئات', href: '/categories' },
    { name: 'الولايات', href: '/wilayas' },
    { name: 'البلديات', href: '/municipalities' },
    { name: 'البحث المتقدم', href: '/search' },
  ],
  services: [
    { name: 'الحالة المدنية', href: '/categories/civil-status' },
    { name: 'المراكز الصحية', href: '/categories/health' },
    { name: 'التعليم', href: '/categories/education' },
    { name: 'المساجد', href: '/categories/mosques' },
    { name: 'النقل', href: '/categories/transport' },
  ],
  support: [
    { name: 'حولنا', href: '/about' },
    { name: 'اتصل بنا', href: '/contact' },
    { name: 'الأسئلة الشائعة', href: '/faq' },
    { name: 'المساعدة', href: '/help' },
    { name: 'الإبلاغ عن مشكلة', href: '/report' },
  ],
  legal: [
    { name: 'سياسة الخصوصية', href: '/privacy' },
    { name: 'شروط الاستخدام', href: '/terms' },
    { name: 'سياسة الكوكيز', href: '/cookies' },
    { name: 'إخلاء المسؤولية', href: '/disclaimer' },
  ]
}

const socialLinks = [
  { name: 'فيسبوك', href: 'https://facebook.com/BldatyDZ', icon: '📘' },
  { name: 'تويتر', href: 'https://twitter.com/BldatyDZ', icon: '🐦' },
  { name: 'إنستغرام', href: 'https://instagram.com/BldatyDZ', icon: '📷' },
  { name: 'يوتيوب', href: 'https://youtube.com/BldatyDZ', icon: '📺' },
  { name: 'لينكد إن', href: 'https://linkedin.com/company/BldatyDZ', icon: '💼' },
]

const wilayas = [
  'الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'سطيف', 'تلمسان', 'بجاية', 'باتنة',
  'ورقلة', 'تبسة', 'بسكرة', 'سكيكدة', 'جيجل', 'المسيلة', 'الشلف'
]

export function Footer() {
  const t = useTranslations('footer')

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">د</span>
              </div>
              <div>
                <div className="text-xl font-bold font-arabic">دليل بلدتي</div>
                <div className="text-sm text-gray-400 font-arabic">كل ما تحتاجه في بلديتك</div>
              </div>
            </div>
            
            <p className="text-gray-300 font-arabic leading-relaxed mb-6">
              منصة شاملة لجميع بلديات الجزائر تساعدك في العثور على جميع المرافق والخدمات 
              في بلديتك بسهولة وسرعة.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-gray-300">
                <MapPinIcon className="w-5 h-5 text-primary-400" />
                <span className="font-arabic">الجزائر العاصمة، الجزائر</span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <PhoneIcon className="w-5 h-5 text-primary-400" />
                <span className="ltr">+213 123 456 789</span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <EnvelopeIcon className="w-5 h-5 text-primary-400" />
                <span className="ltr"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h3 className="text-lg font-semibold font-arabic mb-6">المنصة</h3>
            <ul className="space-y-3">
              {footerLinks.platform.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-primary-400 font-arabic transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services Links */}
          <div>
            <h3 className="text-lg font-semibold font-arabic mb-6">الخدمات</h3>
            <ul className="space-y-3">
              {footerLinks.services.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-primary-400 font-arabic transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold font-arabic mb-6">الدعم</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-primary-400 font-arabic transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Popular Wilayas */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <h3 className="text-lg font-semibold font-arabic mb-6">الولايات الشائعة</h3>
          <div className="flex flex-wrap gap-2">
            {wilayas.map((wilaya) => (
              <Link
                key={wilaya}
                href={`/wilayas/${wilaya.toLowerCase().replace(/\s+/g, '-')}`}
                className="px-3 py-1 bg-gray-800 hover:bg-gray-700 rounded-full text-sm font-arabic transition-colors duration-200"
              >
                {wilaya}
              </Link>
            ))}
          </div>
        </div>

        {/* App Download */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-lg font-semibold font-arabic mb-2">حمل التطبيق الآن</h3>
              <p className="text-gray-300 font-arabic">
                استمتع بتجربة أفضل مع تطبيق دليل بلدتي على هاتفك
              </p>
            </div>
            <div className="flex gap-4">
              <button className="flex items-center gap-3 bg-gray-800 hover:bg-gray-700 px-6 py-3 rounded-lg transition-colors duration-200">
                <DevicePhoneMobileIcon className="w-6 h-6" />
                <div className="text-right">
                  <div className="text-xs text-gray-400">حمل من</div>
                  <div className="font-semibold font-arabic">Google Play</div>
                </div>
              </button>
              <button className="flex items-center gap-3 bg-gray-800 hover:bg-gray-700 px-6 py-3 rounded-lg transition-colors duration-200">
                <DevicePhoneMobileIcon className="w-6 h-6" />
                <div className="text-right">
                  <div className="text-xs text-gray-400">حمل من</div>
                  <div className="font-semibold font-arabic">App Store</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            {/* Copyright */}
            <div className="text-gray-400 font-arabic text-sm">
              © 2024 دليل بلدتي. جميع الحقوق محفوظة.
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4">
              <span className="text-gray-400 font-arabic text-sm">تابعنا:</span>
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center transition-colors duration-200"
                  aria-label={social.name}
                >
                  <span className="text-sm">{social.icon}</span>
                </a>
              ))}
            </div>

            {/* Legal Links */}
            <div className="flex items-center gap-4">
              {footerLinks.legal.map((link, index) => (
                <span key={link.href} className="flex items-center gap-4">
                  <Link 
                    href={link.href}
                    className="text-gray-400 hover:text-primary-400 font-arabic text-sm transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                  {index < footerLinks.legal.length - 1 && (
                    <span className="text-gray-600">|</span>
                  )}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Algeria Flag */}
      <div className="h-2 bg-gradient-to-r from-algeria-green via-algeria-white to-algeria-red"></div>
    </footer>
  )
}
